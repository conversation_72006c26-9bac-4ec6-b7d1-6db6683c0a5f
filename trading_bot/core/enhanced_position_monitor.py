"""Enhanced Position Monitor with Extended Tightening Strategy and TP Proximity Feature."""
import logging
import json
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional

from trading_bot.core.adx_stop_tightener import ADXStopTightener
from trading_bot.core.analytics_utils import (
    create_position_tracking_data, get_timeframe_from_trade_data
)
from trading_bot.core.common_types import PositionInfo
from trading_bot.core.utils import (
    convert_side_to_direction,
    normalize_direction,
    sync_server_time,
    get_server_time_sync_status,
    normalize_symbol_for_bybit,
    count_open_positions_and_orders,
    cancel_order_with_verification  # Import centralized cancellation function
)


class EnhancedPositionMonitor:
    """
    Enhanced Position Monitor with Extended Tightening Strategy and TP Proximity Feature.

    This module extends the original position monitor with:
    - Extended tightening levels up to 6R (removing ATR-based trailing)
    - TP proximity feature that converts SL to trailing stop when within 2% of TP
    """

    def __init__(self, trader, config, logger=None, data_agent=None):
        """
        Initialize the enhanced position monitor.

        Args:
            trader: TradeExecutor instance for API access
            config: Configuration object
            logger: Logger instance
            data_agent: DataAgent instance for analytics tracking
        """
        self.trader = trader
        self.data_agent = data_agent
        self.config = config
        self.logger = logger or logging.getLogger(__name__)

        # Master switch for all position tightening features
        self.enable_position_tightening = getattr(config.trading, 'enable_position_tightening')

        # Configuration for stop loss tightening
        self.enable_sl_tightening = getattr(config.trading, 'enable_sl_tightening')

        # Load RR tightening steps configuration
        self.rr_tightening_steps = {}
        if hasattr(config.trading, 'rr_tightening_steps') and config.trading.rr_tightening_steps:
            if config.trading.rr_tightening_steps.steps:
                # Convert RRTighteningStepConfig objects to dictionaries for easier access
                for step_name, step_config in config.trading.rr_tightening_steps.steps.items():
                    self.rr_tightening_steps[step_name] = {
                        'profit_threshold': step_config.profit_threshold,
                        'sl_position': step_config.sl_position,
                        'description': step_config.description
                    }

        # # If no configurable steps are provided, use default hardcoded steps for backward compatibility
        # if not self.rr_tightening_steps:
        #     self.rr_tightening_steps = {
        #         '1R': {'profit_threshold': 1.0, 'sl_position': 0.3, 'description': 'Partial profit protection'},
        #         '1.5R': {'profit_threshold': 1.5, 'sl_position': 1.0, 'description': 'Breakeven + 1R profit lock'},
        #         '2R': {'profit_threshold': 2.0, 'sl_position': 1.5, 'description': 'Lock 1.5R profit'},
        #         '2.5R': {'profit_threshold': 2.5, 'sl_position': 2.0, 'description': 'Lock 2R profit'},
        #         '3R': {'profit_threshold': 3.0, 'sl_position': 2.5, 'description': 'Lock 2.5R profit'}
        #         # '3.5R': {'profit_threshold': 3.5, 'sl_position': 3.0, 'description': 'Lock 3R profit'},              
        #         # '4R': {'profit_threshold': 4.0, 'sl_position': 3.5, 'description': 'Lock 3.5R profit'},
        #         # '4.5R': {'profit_threshold': 4.5, 'sl_position': 4.0, 'description': 'Lock 4R profit'},
        #         # '5R': {'profit_threshold': 5.0, 'sl_position': 4.5, 'description': 'Lock 4.5R profit'},
        #         # '5.5R': {'profit_threshold': 5.5, 'sl_position': 5.0, 'description': 'Lock 5R profit'},
        #         # '6R': {'profit_threshold': 6.0, 'sl_position': 5.5, 'description': 'Lock 5.5R profit'}
        #     }

        # TP Proximity Feature Configuration
        self.enable_tp_proximity_trailing = getattr(config.trading, 'enable_tp_proximity_trailing')
        self.tp_proximity_threshold_pct = getattr(config.trading, 'tp_proximity_threshold_pct')
        self.tp_proximity_trailing_pct = getattr(config.trading, 'tp_proximity_trailing_pct')

        # Max concurrent trades configuration
        self.max_concurrent_trades = getattr(self.config.trading, 'max_concurrent_trades',3)
        self.logger.info(f"Max concurrent trades limit: {self.max_concurrent_trades}")

        self.logger.info(f"Enhanced Position Monitor initialized - Master tightening: {self.enable_position_tightening}")
        self.logger.info(f"Enhanced Position Monitor initialized - Multi-level SL tightening: {self.enable_sl_tightening}")
        self.logger.info(f"TP Proximity Trailing: {self.enable_tp_proximity_trailing} (threshold: {self.tp_proximity_threshold_pct}%, trailing: {self.tp_proximity_trailing_pct}%)")

        # Initialize ADX Stop Tightener
        self.adx_tightener = ADXStopTightener(self.trader, self.config, self, self.logger)

        # Age-based tightening configuration
        self.age_tightening_enabled = True
        self.max_tightening_pct = 30.0
        self.min_profit_threshold = 1.0

        if hasattr(config.trading, 'age_tightening') and config.trading.age_tightening:
            self.age_tightening_enabled = config.trading.age_tightening.enabled
            self.max_tightening_pct = config.trading.age_tightening.max_tightening_pct
            self.min_profit_threshold = config.trading.age_tightening.min_profit_threshold
            age_bars_config = config.trading.age_tightening.age_tightening_bars or {}
        else:
            # Fallback to old configuration format for backward compatibility
            age_bars_config = getattr(config.trading, 'age_tightening_bars', {})

        timeframe_multipliers = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '4h': 240, '1d': 1440, '1w': 10080
        }

        self.age_thresholds = {}
        for timeframe, bars in age_bars_config.items():
            if timeframe in timeframe_multipliers:
                hours = (bars * timeframe_multipliers[timeframe]) / 60
                self.age_thresholds[timeframe] = timedelta(hours=hours)

        if not self.age_thresholds:
            self.age_thresholds = {
                '1m': timedelta(hours=12), '5m': timedelta(hours=18),
                '15m': timedelta(hours=24), '30m': timedelta(hours=36),
                '1h': timedelta(hours=48), '4h': timedelta(hours=72),
                '1d': timedelta(hours=96), '1w': timedelta(hours=120)
            }

        self.logger.info(f"Age-based tightening configuration - Enabled: {self.age_tightening_enabled}, "
                        f"Max tightening: {self.max_tightening_pct}%, Min profit threshold: {self.min_profit_threshold}R")
        self.logger.info(f"Age-based tightening thresholds loaded: {self.age_thresholds}")

    def _calculate_age_factor(self, trade_created_at, timeframe, current_time=None):
        """Calculate age factor based on weighted thresholds"""
        if current_time is None:
            current_time = datetime.utcnow()

        if current_time.tzinfo is None:
            current_time = current_time.replace(tzinfo=timezone.utc)
        if trade_created_at.tzinfo is None:
            trade_created_at = trade_created_at.replace(tzinfo=timezone.utc)

        age_seconds = (current_time - trade_created_at).total_seconds()
        threshold_seconds = self.age_thresholds.get(timeframe, timedelta(hours=24)).total_seconds()

        # Age factor = 0 when position is younger than threshold
        # Age factor > 0 when position is older than threshold (how many times over threshold)
        if age_seconds <= threshold_seconds:
            return 0.0
        else:
            return (age_seconds - threshold_seconds) / threshold_seconds

    def _calculate_age_tightening(self, age_factor, profit_in_r=None):
        """Calculate tightening factor based on age using configurable settings"""
        if not self.age_tightening_enabled:
            return 0.0

        # Don't apply age-based tightening if position is already profitable above threshold
        if profit_in_r is not None and profit_in_r >= self.min_profit_threshold:
            return 0.0

        if age_factor <= 1.0:
            return 0.0

        # Calculate tightening as percentage of max_tightening_pct
        max_tightening = self.max_tightening_pct / 100.0
        age_tightening = min(age_factor * 0.1, max_tightening)

        return age_tightening

    async def _monitor_max_concurrent_trades(self) -> Dict[str, Any]:
        """
        Monitor and enforce max concurrent trades policy.

        Returns:
            Dict with monitoring results and any actions taken
        """
        try:
            self.logger.info("🔍 Monitoring max concurrent trades policy...")

            # Get current active trades count
            active_trades_count = await self._count_active_trades()

            max_concurrent_trades = getattr(self.config.trading, 'max_concurrent_trades', 3)
            self.logger.info(f"📊 Current active trades: {active_trades_count}/{max_concurrent_trades}")

            # Check if we're within limits
            if active_trades_count <= max_concurrent_trades:
                return {
                    "status": "within_limits",
                    "active_trades": active_trades_count,
                    "max_allowed": max_concurrent_trades,
                    "message": f"Active trades ({active_trades_count}) within limit ({max_concurrent_trades})"
                }

            # We're over the limit - need to cancel excess orders
            excess_count = active_trades_count - max_concurrent_trades
            self.logger.warning(f"⚠️ Over max concurrent trades limit! Active: {active_trades_count}, Limit: {max_concurrent_trades}")

            # Cancel excess entry orders
            cancel_results = await self._cancel_excess_entry_orders(excess_count)

            return {
                "status": "enforced",
                "active_trades": active_trades_count,
                "max_allowed": max_concurrent_trades,
                "excess_cancelled": len(cancel_results),
                "cancel_results": cancel_results,
                "message": f"Cancelled {len(cancel_results)} excess entry orders to enforce limit"
            }

        except Exception as e:
            error_msg = f"Error monitoring max concurrent trades: {str(e)}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg
            }

    async def _count_active_trades(self) -> int:
        """
        Count current active trades (positions + entry orders).

        IMPORTANT: Only counts positions and entry orders. TP/SL orders are NOT counted
        as they are attached to positions and don't consume separate slots.

        Returns:
            Number of active trades
        """
        try:
            # Try to use risk_manager's slot counting if available
            if hasattr(self.trader, 'risk_manager') and self.trader.risk_manager:
                try:
                    # Use unified SlotManager for consistent slot counting
                    available_slots, slot_details = self.trader.risk_manager.slot_manager.get_available_order_slots()
                    occupied_slots = slot_details.get('total_occupied_slots')
                    max_slots = slot_details.get('max_concurrent_trades')
                    self.logger.debug(f"Using SlotManager slot count: {occupied_slots} occupied slots")
                    if occupied_slots is not None:
                        return occupied_slots
                    # If occupied_slots is None, fall through to utility function
                    self.logger.warning(f"SlotManager returned None for occupied_slots, falling back to utility function")
                except Exception as e:
                    self.logger.warning(f"Could not use risk_manager slot count: {e}")

            # Use the centralized count_open_positions_and_orders utility function
            # The utility function will handle API calls internally when trader is provided
            count_result = count_open_positions_and_orders(trader=self.trader)

            # Get the total active trades count
            total_active = count_result.get('active_positions_count', 0) + count_result.get('open_entry_orders_count', 0)

            # Log detailed breakdown for debugging
            self.logger.debug(f"Utility function count - Active positions: {count_result.get('active_positions_count', 0)}, "
                            f"Entry orders: {count_result.get('open_entry_orders_count', 0)}, "
                            f"TP/SL orders: {count_result.get('take_profit_orders', 0) + count_result.get('stop_loss_orders', 0)}, "
                            f"Total active: {total_active}")

            return total_active

        except Exception as e:
            self.logger.error(f"Error counting active trades: {e}")
            return 0

    async def _cancel_excess_entry_orders(self, excess_count: int) -> List[Dict[str, Any]]:
        """
        Cancel excess entry orders to enforce max concurrent trades limit.
        Uses utility function data directly - single API call approach.
        """
        try:
            cancel_results = []

            # Use the utility function to get precise breakdown of positions and orders
            # This makes ONE API call and returns all the data we need
            count_result = count_open_positions_and_orders(trader=self.trader)
            open_entry_orders = count_result.get('open_entry_orders', {})
            raw_orders = count_result.get('raw_orders', [])

            self.logger.info(f"Entry orders by symbol: {open_entry_orders}")
            self.logger.info(f"Total raw orders from utility: {len(raw_orders)}")

            if not open_entry_orders:
                self.logger.warning("No entry orders found to cancel")
                return cancel_results

            # Create a map of symbol -> list of orders for that symbol from the raw data
            symbol_orders = {}
            for order in raw_orders:
                symbol = order.get('symbol')
                if symbol and symbol in open_entry_orders:  # Only include symbols with excess orders
                    if symbol not in symbol_orders:
                        symbol_orders[symbol] = []
                    symbol_orders[symbol].append(order)

            # Sort symbols by number of entry orders (most orders first)
            sorted_symbols = sorted(open_entry_orders.keys(),
                                  key=lambda s: open_entry_orders[s],
                                  reverse=True)

            cancelled_count = 0
            for symbol in sorted_symbols:
                if cancelled_count >= excess_count:
                    break

                orders_for_symbol = symbol_orders.get(symbol, [])
                if not orders_for_symbol:
                    continue

                # Sort orders by creation time (oldest first)
                orders_for_symbol.sort(key=lambda x: x.get('createdTime', ''))

                # Cancel oldest orders for this symbol
                for order in orders_for_symbol:
                    if cancelled_count >= excess_count:
                        break

                    order_id = order.get('orderId')
                    order_link_id = order.get('orderLinkId')

                    self.logger.info(f"Cancelling excess order: {symbol} (ID: {order_id or order_link_id})")

                    # Cancel the order using centralized function
                    cancel_result = cancel_order_with_verification(
                        trader=self.trader,
                        symbol=symbol,
                        order_id=order_id,
                        order_link_id=order_link_id,
                        max_retries=3,
                        retry_delay=0.5
                    )

                    if cancel_result.get("retCode") == 0:
                        self.logger.info(f"✅ Successfully cancelled: {symbol}")
                        cancel_results.append({
                            "symbol": symbol,
                            "order_id": order_id,
                            "order_link_id": order_link_id,
                            "status": "cancelled"
                        })
                        cancelled_count += 1
                    else:
                        error_msg = cancel_result.get("retMsg", "Unknown error")
                        # Check if it's a "too late to cancel" error - this is actually OK
                        if "too late to cancel" in error_msg.lower() or "order not exists" in error_msg.lower():
                            self.logger.info(f"ℹ️ Order already processed: {symbol} (ID: {order_id or order_link_id})")
                            cancel_results.append({
                                "symbol": symbol,
                                "order_id": order_id,
                                "order_link_id": order_link_id,
                                "status": "already_processed"
                            })
                            cancelled_count += 1  # Count as successful since order is no longer active
                        else:
                            self.logger.error(f"❌ Failed to cancel {symbol}: {error_msg}")
                            cancel_results.append({
                                "symbol": symbol,
                                "status": "failed",
                                "error": error_msg
                            })

            self.logger.info(f"Cancelled {cancelled_count}/{excess_count} excess entry orders")
            return cancel_results

        except Exception as e:
            self.logger.error(f"Error in cancel_excess_entry_orders: {e}")
            return [{"status": "error", "error": str(e)}]

    async def monitor_positions(self) -> Dict[str, Any]:
        """
        Monitor all open positions and apply enhanced tightening strategy.

        Returns:
            Dict with monitoring results
        """
        try:
            # Synchronize server time
            self.logger.info("Initiating server time synchronization...")
            sync_server_time(self.trader.api_manager.session)
            sync_status = get_server_time_sync_status()

            if sync_status["is_synced"]:
                offset_seconds = sync_status["offset_ms"] / 1000
                self.logger.info(f"🕐 Server time sync successful (offset: {offset_seconds:.2f}s)")
            else:
                self.logger.warning(f"❌ Server time sync failed. Offset: {sync_status['offset_ms']}ms")

            # Check and enforce max concurrent trades policy (always run this)
            max_trades_result = await self._monitor_max_concurrent_trades()
            if max_trades_result.get("status") == "enforced":
                self.logger.info(f"Max concurrent trades policy enforced: {max_trades_result.get('message')}")

            # Get all open positions (always collect position data for table display)
            positions = await self._get_open_positions()

            if not positions:
                return {"status": "success", "message": "No open positions to monitor", "positions_checked": 0, "all_positions": []}

            # Always calculate R values for all positions for table display
            all_positions_with_r = []
            for position in positions:
                try:
                    direction = normalize_direction(convert_side_to_direction(position.side))
                    entry_price = position.entry_price
                    current_price = position.current_price
                    one_r = position.risk_amount

                    if direction == 'LONG':
                        profit_distance = current_price - entry_price
                    else:
                        profit_distance = entry_price - current_price

                    profit_in_r = profit_distance / one_r if one_r > 0 else 0
                    all_positions_with_r.append({
                        "symbol": position.symbol,
                        "profit_r": profit_in_r
                    })
                except Exception as e:
                    self.logger.warning(f"Could not calculate R for {position.symbol}: {e}")

            # Only apply tightening logic if enabled
            if not self.enable_position_tightening:
                return {
                    "status": "disabled",
                    "message": "All position tightening is disabled (master switch)",
                    "positions_checked": len(positions),
                    "all_positions": all_positions_with_r,
                    "max_trades_monitoring": max_trades_result
                }

            if not self.enable_sl_tightening:
                return {
                    "status": "disabled",
                    "message": "Stop loss tightening is disabled",
                    "positions_checked": len(positions),
                    "all_positions": all_positions_with_r,
                    "max_trades_monitoring": max_trades_result
                }

            self.logger.info(f"📊 Monitoring {len(positions)} open positions for enhanced SL tightening")

            tightening_results = []

            for position in positions:
                try:
                    # DEBUG: Log position data before tightening
                    self.logger.info(f"🔍 DEBUG MONITOR {position.symbol}: Processing position - "
                                   f"entry_price={position.entry_price}, current_price={position.current_price}, "
                                   f"current_stop_loss={position.current_stop_loss}, risk_amount={position.risk_amount}, "
                                   f"last_tightened_milestone={getattr(position, 'last_tightened_milestone', 'None')}")

                    # Apply enhanced tightening logic
                    result = await self._check_and_tighten_stop_loss_enhanced(position)
                    if result:
                        tightening_results.append(result)
                        self.logger.info(f"🔍 DEBUG MONITOR {position.symbol}: Tightening result - {result}")
                except Exception as e:
                    self.logger.error(f"Error processing enhanced tightening for position {position.symbol}: {e}")
                    tightening_results.append({
                        "symbol": position.symbol,
                        "status": "error",
                        "error": str(e),
                        "type": "Enhanced_Tightening"
                    })

            # Apply ADX-based tightening if enabled
            adx_tightening_results = []
            if self.adx_tightener.enable_adx_tightening:
                adx_tightening_results = await self.adx_tightener.check_and_tighten_adx_stop(positions)
            tightening_results.extend(adx_tightening_results)

            # Summary
            successful_tightenings = len([r for r in tightening_results if r.get("status") == "tightened"])

            return {
                "status": "success",
                "positions_checked": len(positions),
                "tightenings_applied": successful_tightenings,
                "results": tightening_results,
                "all_positions": all_positions_with_r,
                "max_trades_monitoring": max_trades_result
            }

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"❌ Critical error in enhanced position monitoring: {error_msg}")
            self.logger.error(f"❌ Exception type: {type(e).__name__}")
            self.logger.error(f"❌ Exception args: {e.args}")
            import traceback
            self.logger.error(f"❌ Full traceback: {traceback.format_exc()}")

            # Provide more specific error information for common issues
            if "api_manager" in error_msg.lower() or "get_positions" in error_msg.lower():
                self.logger.error("❌ API Error: Failed to retrieve positions from exchange")
                return {"status": "error", "error": f"API Error: {error_msg}", "error_type": "api_failure"}
            elif "current_price" in error_msg.lower() or "price" in error_msg.lower():
                self.logger.error("❌ Price Data Error: Failed to get current price data")
                return {"status": "error", "error": f"Price Data Error: {error_msg}", "error_type": "price_data_failure"}
            elif "database" in error_msg.lower() or "data_agent" in error_msg.lower():
                self.logger.error("❌ Database Error: Failed to access trade data")
                return {"status": "error", "error": f"Database Error: {error_msg}", "error_type": "database_failure"}
            else:
                self.logger.error("❌ Unknown Error: Unexpected error in position monitoring")
                return {"status": "error", "error": f"Unknown Error: {error_msg}", "error_type": "unexpected_error"}

    async def _check_and_tighten_stop_loss_enhanced(self, position: PositionInfo) -> Optional[Dict[str, Any]]:
        """
        Enhanced stop loss tightening with extended levels and TP proximity feature.

        Args:
            position: PositionInfo object

        Returns:
            Dict with tightening result or None if no action taken
        """
        try:
            direction = normalize_direction(convert_side_to_direction(position.side))
            entry_price = position.entry_price
            current_price = position.current_price
            current_sl = position.current_stop_loss
            current_tp = position.current_take_profit
            one_r = position.risk_amount

            # DEBUG: Log position data integrity
            self.logger.info(f"🔍 DEBUG {position.symbol}: direction={direction}, entry_price={entry_price}, "
                           f"current_price={current_price}, current_sl={current_sl}, one_r={one_r}")

            if not current_sl or not one_r:
                self.logger.warning(f"⚠️ DEBUG {position.symbol}: Missing current_sl or one_r - current_sl={current_sl}, one_r={one_r}")
                return None

            # Calculate profit in R
            if direction == 'LONG':
                profit_distance = current_price - entry_price
            else:  # SHORT
                profit_distance = entry_price - current_price

            profit_in_r = profit_distance / one_r if one_r > 0 else 0

            # DEBUG: Log profit calculation
            self.logger.info(f"🔍 DEBUG {position.symbol}: profit_distance={profit_distance:.6f}, "
                           f"profit_in_r={profit_in_r:.2f}R, current_milestone={self._get_current_milestone(profit_in_r)}")

            # Check if position has already been tightened at this milestone
            current_milestone = self._get_current_milestone(profit_in_r)
            last_milestone = position.last_tightened_milestone

            # Special handling for TP_PROXIMITY milestone
            if last_milestone == "TP_PROXIMITY":
                # If TP_PROXIMITY was the last tightening, we need to check if we should still apply it
                # or if we can proceed with normal tightening based on current profit level
                if current_milestone and profit_in_r >= 1.0:
                    # Allow normal tightening progression after TP_PROXIMITY
                    # Only skip if we're still at the same profit level that triggered TP_PROXIMITY
                    self.logger.info(f"🔄 DEBUG {position.symbol}: TP_PROXIMITY was last tightening, "
                                   f"checking if normal tightening can proceed (current: {current_milestone})")
                else:
                    self.logger.info(f"⚠️ DEBUG {position.symbol}: TP_PROXIMITY already applied, "
                                   f"skipping further tightening at current profit level {profit_in_r:.2f}R")
                    return {
                        "status": "skipped",
                        "reason": "TP_PROXIMITY already applied"
                    }
            elif current_milestone and last_milestone == current_milestone:
                self.logger.info(f"⚠️ DEBUG {position.symbol}: Already tightened at {current_milestone} milestone "
                               f"(last_tightened={last_milestone})")
                return {
                    "status": "skipped",
                    "reason": f"Already tightened at {current_milestone} milestone"
                }

            # Skip if not profitable enough
            if profit_in_r < 1.0:
                self.logger.info(f"⚠️ DEBUG {position.symbol}: Profit {profit_in_r:.2f}R < 1.0R minimum")

                # Check if position is old enough for age-based tightening
                if not (position.timeframe and position.created_at):
                    if position.has_trade_data:
                        self.logger.warning(f"⚠️ DEBUG {position.symbol}: Missing timeframe or created_at for age-based tightening despite having trade data")
                    return {
                        "status": "skipped",
                        "reason": f"Profit {profit_in_r:.2f}R < 1.0R minimum"
                    }

                age_factor = self._calculate_age_factor(position.created_at, position.timeframe)
                age_tightening = self._calculate_age_tightening(age_factor, profit_in_r)

                self.logger.info(f"🔍 DEBUG {position.symbol}: age_factor={age_factor:.2f}, age_tightening={age_tightening:.3f}")

                if age_tightening <= 0:
                    self.logger.info(f"⚠️ DEBUG {position.symbol}: Not old enough for age-based tightening")
                    return {
                        "status": "skipped",
                        "reason": f"Profit {profit_in_r:.2f}R < 1.0R minimum, not old enough for age-based tightening"
                    }

                self.logger.info(f"✅ DEBUG {position.symbol}: Old enough for age-based tightening "
                               f"(age_factor={age_factor:.2f}, tightening={age_tightening:.3f})")

            # Enhanced multi-level tightening logic
            proposed_sl = current_sl
            tightening_level = "none"
            target_milestone = None

            # DEBUG: Log tightening logic start
            self.logger.info(f"🔍 DEBUG {position.symbol}: Starting tightening logic - current_sl={current_sl}, profit_in_r={profit_in_r:.2f}")

            # Apply tightening based on profit level
            if direction == 'LONG':
                proposed_sl, tightening_level, target_milestone = self._apply_long_tightening(
                    entry_price, current_price, current_sl, one_r, profit_in_r
                )
            else:  # SHORT
                proposed_sl, tightening_level, target_milestone = self._apply_short_tightening(
                    entry_price, current_price, current_sl, one_r, profit_in_r
                )

            # DEBUG: Log tightening result
            self.logger.info(f"🔍 DEBUG {position.symbol}: Tightening result - proposed_sl={proposed_sl}, "
                           f"tightening_level={tightening_level}, target_milestone={target_milestone}")

            # Check TP proximity feature
            if self.enable_tp_proximity_trailing and current_tp:
                self.logger.info(f"🔍 DEBUG {position.symbol}: Checking TP proximity - current_tp={current_tp}")
                tp_proximity_result = self._check_tp_proximity(
                    entry_price, current_price, current_tp, direction, position.symbol
                )
                if tp_proximity_result:
                    self.logger.info(f"✅ DEBUG {position.symbol}: TP proximity triggered - converting to trailing stop")
                    # Apply the trailing stop by setting the proposed SL to the trailing level
                    proposed_sl = tp_proximity_result.get("trailing_stop_level", proposed_sl)
                    tightening_level = f"TP_PROXIMITY_{tp_proximity_result.get('trailing_pct', 1.0)}%"
                    target_milestone = "TP_PROXIMITY"
                    # Continue with normal tightening flow to actually apply the stop loss

            # Check if we have a valid tightening
            sl_diff = abs(proposed_sl - current_sl)
            self.logger.info(f"🔍 DEBUG {position.symbol}: Validating tightening - sl_diff={sl_diff:.8f}, tightening_level={tightening_level}")

            # Use a more reasonable minimum difference for crypto pairs (typically 0.0001)
            min_sl_diff = 1e-8
            if sl_diff < min_sl_diff or tightening_level == "none":
                self.logger.warning(f"⚠️ DEBUG {position.symbol}: No valid tightening - sl_diff={sl_diff:.8f} < {min_sl_diff:.8f}, tightening_level={tightening_level}")
                return {
                    "status": "skipped",
                    "reason": f"No valid tightening at {profit_in_r:.2f}R profit (diff too small)"
                }

            # Apply the stop loss tightening
            result = await self._update_stop_loss(position, proposed_sl)

            if result.get("success"):
                reasoning_data = {
                    'tightening_type': 'Enhanced_RR_Based_Tightening',
                    'profit_in_r': round(profit_in_r, 2),
                    'tightening_level': tightening_level,
                    'entry_price': entry_price,
                    'target_milestone': target_milestone
                }

                # Update the last tightened milestone
                if target_milestone and self.data_agent:
                    try:
                        # Get the actual trade ID from the original trade data
                        trade_id = None
                        if hasattr(position, 'symbol'):
                            # Try to find the trade using the symbol
                            original_trade_data = await self._get_original_trade_data(position.symbol, position.entry_price)
                            if original_trade_data and 'id' in original_trade_data:
                                trade_id = original_trade_data['id']
                                self.logger.debug(f"Found trade ID {trade_id} for {position.symbol}")

                        if trade_id:
                            update_success = self.data_agent.update_trade(
                                trade_id,
                                last_tightened_milestone=target_milestone,
                                alteration_details=json.dumps(reasoning_data)
                            )
                            if update_success:
                                self.logger.info(f"✅ Updated last tightened milestone for {position.symbol} (Trade ID: {trade_id}) to {target_milestone}")
                            else:
                                self.logger.warning(f"⚠️ Database update returned False for {position.symbol} (Trade ID: {trade_id})")
                        else:
                            self.logger.warning(f"⚠️ Could not find trade ID for {position.symbol} - milestone not updated in database")
                    except Exception as e:
                        self.logger.error(f"❌ Failed to update last tightened milestone for {position.symbol}: {e}")

                return {
                    "symbol": position.symbol,
                    "status": "tightened",
                    "direction": direction,
                    "old_stop_loss": current_sl,
                    "new_stop_loss": proposed_sl,
                    "profit_r": profit_in_r,
                    "tightening_level": tightening_level,
                    "reasoning": reasoning_data
                }
            else:
                return {
                    "symbol": position.symbol,
                    "status": "failed",
                    "error": result.get("error", "Unknown error")
                }

        except Exception as e:
            self.logger.error(f"Error in enhanced tightening for {position.symbol}: {e}")
            return {
                "symbol": position.symbol,
                "status": "error",
                "error": str(e)
            }

    def _get_current_milestone(self, profit_in_r: float) -> Optional[str]:
        """Determine the current milestone based on profit level using configurable steps"""
        # Sort steps by profit threshold in descending order to check highest levels first
        sorted_steps = sorted(self.rr_tightening_steps.items(),
                            key=lambda x: x[1]['profit_threshold'],
                            reverse=True)

        for milestone, step_config in sorted_steps:
            if profit_in_r >= step_config['profit_threshold']:
                return milestone

        return None

    def _apply_long_tightening(self, entry_price: float, current_price: float,
                                current_sl: float, one_r: float, profit_in_r: float):
        """Apply tightening logic for long positions using configurable RR steps"""
        proposed_sl = current_sl
        tightening_level = "none"
        target_milestone = None

        # DEBUG: Log tightening evaluation start
        self.logger.info(f"🔍 DEBUG LONG TIGHTENING: entry_price={entry_price}, current_price={current_price}, "
                         f"current_sl={current_sl}, one_r={one_r}, profit_in_r={profit_in_r:.2f}")

        # Check each configured RR step
        for milestone, step_config in self.rr_tightening_steps.items():
            profit_threshold = step_config['profit_threshold']
            sl_position = step_config['sl_position']

            # Check if current profit meets this step's threshold
            price_target = entry_price + profit_threshold * one_r
            profit_condition = profit_in_r >= profit_threshold

            self.logger.info(f"🔍 DEBUG {milestone}: profit_threshold={profit_threshold}, "
                           f"price_target={price_target}, profit_condition={profit_condition}")

            if profit_condition:
                new_proposed_sl = entry_price + sl_position * one_r
                sl_valid = new_proposed_sl > proposed_sl and new_proposed_sl < current_price

                self.logger.info(f"🔍 DEBUG {milestone}: new_proposed_sl={new_proposed_sl}, "
                               f"sl_valid={sl_valid}, current_price={current_price}")

                if sl_valid:
                    proposed_sl = new_proposed_sl
                    tightening_level = milestone
                    target_milestone = milestone
                    self.logger.info(f"✅ DEBUG {milestone}: Tightening applied! proposed_sl={proposed_sl}")

        return proposed_sl, tightening_level, target_milestone

    def _apply_short_tightening(self, entry_price: float, current_price: float,
                                current_sl: float, one_r: float, profit_in_r: float):
        """Apply tightening logic for short positions using configurable RR steps"""
        proposed_sl = current_sl
        tightening_level = "none"
        target_milestone = None

        # Check each configured RR step
        for milestone, step_config in self.rr_tightening_steps.items():
            profit_threshold = step_config['profit_threshold']
            sl_position = step_config['sl_position']

            # Check if current profit meets this step's threshold
            price_target = entry_price - profit_threshold * one_r
            profit_condition = profit_in_r >= profit_threshold

            self.logger.info(f"🔍 DEBUG SHORT {milestone}: profit_threshold={profit_threshold}, "
                           f"price_target={price_target}, profit_condition={profit_condition}")

            if profit_condition:
                new_proposed_sl = entry_price - sl_position * one_r
                sl_valid = new_proposed_sl < proposed_sl and new_proposed_sl > current_price

                self.logger.info(f"🔍 DEBUG SHORT {milestone}: new_proposed_sl={new_proposed_sl}, "
                               f"sl_valid={sl_valid}, current_price={current_price}")

                if sl_valid:
                    proposed_sl = new_proposed_sl
                    tightening_level = milestone
                    target_milestone = milestone
                    self.logger.info(f"✅ DEBUG SHORT {milestone}: Tightening applied! proposed_sl={proposed_sl}")

        return proposed_sl, tightening_level, target_milestone

    def _check_tp_proximity(self, entry_price: float, current_price: float,
                           take_profit: float, direction: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Check if position is within TP proximity threshold and convert to trailing stop"""
        try:
            if direction == 'LONG':
                distance_to_tp = abs(take_profit - current_price)
                distance_pct = (distance_to_tp / current_price) * 100
            else:  # SHORT
                distance_to_tp = abs(current_price - take_profit)
                distance_pct = (distance_to_tp / current_price) * 100

            if distance_pct <= self.tp_proximity_threshold_pct:
                # Calculate trailing stop level
                if direction == 'LONG':
                    trailing_sl = current_price * (1 - self.tp_proximity_trailing_pct / 100)
                else:  # SHORT
                    trailing_sl = current_price * (1 + self.tp_proximity_trailing_pct / 100)

                self.logger.info(f"🎯 TP PROXIMITY ACTIVATED for {symbol}: "
                               f"Distance to TP: {distance_pct:.2f}% (threshold: {self.tp_proximity_threshold_pct}%)")

                # Note: In a real implementation, you would need to modify the exchange API call
                # to set a trailing stop order instead of a regular stop loss
                return {
                    "symbol": symbol,
                    "status": "tp_proximity_activated",
                    "direction": direction,
                    "distance_to_tp_pct": distance_pct,
                    "trailing_stop_level": trailing_sl,
                    "trailing_pct": self.tp_proximity_trailing_pct,
                    "message": f"TP proximity activated - would convert to {self.tp_proximity_trailing_pct}% trailing stop"
                }

            return None

        except Exception as e:
            self.logger.error(f"Error checking TP proximity for {symbol}: {e}")
            return None

    async def _get_open_positions(self) -> List[PositionInfo]:
        """Get all open positions from the exchange"""
        try:
            positions_response = self.trader.api_manager.get_positions(category="linear", settleCoin="USDT")

            if positions_response.get("error"):
                error_msg = positions_response['error']
                self.logger.error(f"❌ Positions API call failed: {error_msg}")
                return []

            if positions_response.get("retCode") != 0:
                error_msg = positions_response.get("retMsg", "Unknown API error")
                self.logger.error(f"❌ Positions API returned error code: {positions_response.get('retCode')} - {error_msg}")
                return []

            positions_list = positions_response.get("result", {}).get("list", [])
            self.logger.info(f"📊 Found {len(positions_list)} total positions from exchange")

            open_positions = []
            for pos in positions_list:
                try:
                    size = float(pos.get('size', 0))
                    if size > 0:
                        symbol = pos.get('symbol')
                        self.logger.debug(f"🔍 Processing position for {symbol}")

                        current_price = self.trader.get_last_close_price(symbol, "1m")

                        if current_price is None:
                            self.logger.warning(f"⚠️ Could not get current price for {symbol} - skipping position")
                            continue

                        entry_price = float(pos.get('avgPrice', 0))
                        current_sl = pos.get('stopLoss')
                        current_sl_price = float(current_sl) if current_sl and current_sl != "0" else None

                        # Get original trade data
                        original_risk_amount = 0
                        original_trade_data = await self._get_original_trade_data(symbol, entry_price)

                        if original_trade_data:
                            original_entry = original_trade_data.get('entry_price', entry_price)
                            original_sl = original_trade_data.get('stop_loss', current_sl_price)
                            if original_entry and original_sl:
                                original_risk_amount = abs(float(original_entry) - float(original_sl))

                        if original_risk_amount == 0 and current_sl_price and entry_price:
                            original_risk_amount = abs(entry_price - current_sl_price)

                        # Extract timeframe and created_at
                        timeframe = None
                        created_at = None
                        has_trade_data = original_trade_data is not None
                        if original_trade_data:
                            timeframe = original_trade_data.get('timeframe')
                            # If timeframe is missing but we have recommendation_id, try to get it from analysis_results
                            if not timeframe and original_trade_data.get('recommendation_id'):
                                try:
                                    timeframe = get_timeframe_from_trade_data(original_trade_data, self.trader.position_manager.data_agent)
                                except Exception as e:
                                    self.logger.warning(f"Could not get timeframe from trade data for {symbol}: {e}")

                            created_at_str = original_trade_data.get('created_at') or original_trade_data.get('timestamp')
                            if created_at_str:
                                try:
                                    if isinstance(created_at_str, str):
                                        if 'T' in created_at_str:
                                            created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
                                        else:
                                            created_at = datetime.strptime(created_at_str, "%Y-%m-%d %H:%M:%S")
                                            created_at = created_at.replace(tzinfo=timezone.utc)
                                    elif isinstance(created_at_str, (int, float)):
                                        created_at = datetime.fromtimestamp(created_at_str, tz=timezone.utc)
                                except Exception as parse_error:
                                    self.logger.warning(f"Could not parse created_at timestamp for {symbol}: {parse_error}")

                        position_info = PositionInfo(
                            symbol=symbol,
                            side=pos.get('side', 'Buy'),
                            size=size,
                            entry_price=entry_price,
                            current_price=current_price,
                            unrealized_pnl=float(pos.get('unrealisedPnl', 0)),
                            current_stop_loss=current_sl_price,
                            current_take_profit=float(pos.get('takeProfit')) if pos.get('takeProfit') and pos.get('takeProfit') != "0" else None,
                            position_idx=int(pos.get('positionIdx', 0)),
                            risk_amount=original_risk_amount,
                            timeframe=timeframe,
                            created_at=created_at,
                            last_tightened_milestone=original_trade_data.get('last_tightened_milestone') if original_trade_data else None,
                            has_trade_data=has_trade_data
                        )

                        open_positions.append(position_info)
                        self.logger.debug(f"✅ Successfully processed position for {symbol}")

                except Exception as e:
                    self.logger.error(f"❌ Error processing individual position {pos.get('symbol', 'unknown')}: {e}")
                    continue

            self.logger.info(f"✅ Successfully processed {len(open_positions)} open positions")
            return open_positions

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"❌ Critical error getting open positions: {error_msg}")
            return []

    async def _get_original_trade_data(self, symbol: str, entry_price: float) -> Optional[Dict[str, Any]]:
        """Get original trade data from database"""
        try:
            if not hasattr(self.trader, 'position_manager') or not hasattr(self.trader.position_manager, 'data_agent'):
                self.logger.warning("No data_agent available for trade lookup")
                return None

            data_agent = self.trader.position_manager.data_agent

            symbols_to_try = [symbol]
            if not symbol.endswith('.P'):
                symbols_to_try.append(f"{symbol}.P")
            if symbol.endswith('.P'):
                symbols_to_try.append(symbol[:-2])

            for symbol_variant in symbols_to_try:
                open_trades = data_agent.get_trades(symbol=symbol_variant, status='open')
                if open_trades:
                    open_trades.sort(key=lambda t: t.get('created_at', t.get('timestamp', '')), reverse=True)

                    price_tolerance = entry_price * 0.02
                    for trade in open_trades:
                        trade_entry = trade.get('entry_price')
                        if trade_entry:
                            trade_entry_float = float(trade_entry)
                            price_diff = abs(trade_entry_float - entry_price)
                            if price_diff <= price_tolerance:
                                return trade

                    return open_trades[0] if open_trades else None

                all_trades = data_agent.get_trades(symbol=symbol_variant)
                if all_trades:
                    all_trades.sort(key=lambda t: t.get('created_at', t.get('timestamp', '')), reverse=True)

                    price_tolerance = entry_price * 0.02
                    for trade in all_trades:
                        trade_entry = trade.get('entry_price')
                        if trade_entry:
                            trade_entry_float = float(trade_entry)
                            price_diff = abs(trade_entry_float - entry_price)
                            if price_diff <= price_tolerance:
                                return trade

                    return all_trades[0] if all_trades else None

            return None

        except Exception as e:
            self.logger.error(f"Error getting original trade data for {symbol}: {e}")
            return None

    async def _update_stop_loss(self, position: PositionInfo, new_stop_loss: float) -> Dict[str, Any]:
        """
        Update the stop loss for a position using Bybit API.

        Args:
            position: PositionInfo object
            new_stop_loss: New stop loss price

        Returns:
            Dict with update result
        """
        try:
            # Format stop loss price to appropriate precision
            formatted_sl = f"{new_stop_loss:.6f}"

            # Call Bybit API to update stop loss
            response = self.trader.api_manager.set_trading_stop(
                category="linear",
                symbol=position.symbol,
                stopLoss=formatted_sl,
                tpslMode="Full",  # Update entire position
                positionIdx=position.position_idx,
                recv_window=self.config.bybit.recv_window
            )

            if response.get("retCode") == 0:
                self.logger.info(f"Successfully updated SL for {position.symbol} to {formatted_sl}")
                return {"success": True, "response": response}
            else:
                error_msg = response.get("retMsg", "Unknown API error")
                self.logger.error(f"Failed to update SL for {position.symbol}: {error_msg}")
                return {"success": False, "error": error_msg, "response": response}

        except Exception as e:
            # Handle specific Bybit error codes
            error_str = str(e)
            if "34040" in error_str or "not modified" in error_str.lower():
                # This means the stop loss is already at the target value
                self.logger.warning(f"Stop loss for {position.symbol} already at target value: {new_stop_loss:.8f}")
                return {"success": True, "warning": "Stop loss already at target value"}
            else:
                self.logger.error(f"Exception updating SL for {position.symbol}: {e}")
                return {"success": False, "error": str(e)}

    def get_status(self) -> Dict[str, Any]:
        """Get status of the enhanced position monitor"""
        return {
            "enabled": self.enable_sl_tightening,
            "tp_proximity_enabled": self.enable_tp_proximity_trailing,
            "tp_proximity_threshold_pct": self.tp_proximity_threshold_pct,
            "tp_proximity_trailing_pct": self.tp_proximity_trailing_pct,
            "extended_tightening_levels": True,
            "max_tightening_level": "6R"
        }

    def _get_recv_window(self) -> int:
        """Get receive window for API calls (compatibility with ADX tightener)"""
        return getattr(self.config.bybit, 'recv_window', 60000)

    async def _get_atr(self, symbol: str, period: int = 14) -> Optional[float]:
        """Calculate ATR for a symbol (compatibility method)"""
        try:
            response = self.trader.api_manager.get_kline(
                category="linear",
                symbol=symbol,
                interval="60",  # 1 hour candles
                limit=period + 1,
            )

            if not response or response.get("retCode") != 0 or not response.get("result", {}).get("list"):
                self.logger.warning(f"Could not get klines for ATR calculation: {symbol}")
                return None

            klines = response["result"]["list"]

            if len(klines) < period + 1:
                self.logger.warning(f"Insufficient klines for ATR calculation: {symbol}")
                return None

            # Calculate True Range for each period
            true_ranges = []

            for i in range(1, len(klines)):
                current_high = float(klines[i][2])
                current_low = float(klines[i][3])
                prev_close = float(klines[i-1][4])

                tr1 = current_high - current_low
                tr2 = abs(current_high - prev_close)
                tr3 = abs(current_low - prev_close)

                true_range = max(tr1, tr2, tr3)
                true_ranges.append(true_range)

            # Calculate ATR as simple moving average of True Ranges
            if len(true_ranges) >= period:
                atr = sum(true_ranges[-period:]) / period
                return atr

            return None

        except Exception as e:
            self.logger.error(f"Error calculating ATR for {symbol}: {e}")
            return None
