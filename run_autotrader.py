#!/usr/bin/env python3
"""Standalone autotrader for the agentic trading bot."""
import asyncio
import io
import logging
import os
import signal
import sys
import time
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Optional, Dict, List, TYPE_CHECKING

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # python-dotenv not available, skip .env loading
    pass

if TYPE_CHECKING:
    import openai
else:
    try:
        import openai
    except ImportError:
        openai = None


# Import logging configuration first to set ERROR level globally
from trading_bot.core.logging_config import configure_run_autotrader_logging, setup_comprehensive_console_logging

# Configure logging specifically for run_autotrader
logger = configure_run_autotrader_logging()

# Set up comprehensive console logging to capture all output
setup_comprehensive_console_logging()

from trading_bot.config.settings import Config
from trading_bot.core.sourcer import ChartSourcer
from trading_bot.core.analyzer import ChartAnalyzer
from trading_bot.core.validator import SignalValidator
from trading_bot.core.trader import TradeExecutor
from trading_bot.core.data_agent import DataAgent
from trading_bot.core.db_queue import DatabaseQueue
from trading_bot.core.recommender import Recommender
from trading_bot.core.recommendation_service import RecommendationService
from trading_bot.core.cleaner import ChartCleaner
from trading_bot.core.position_analyzer import PositionAnalyzer
from trading_bot.core.position_manager import PositionManager
from trading_bot.core.risk_manager import RiskManager
from trading_bot.core.timestamp_validator import TimestampValidator
from trading_bot.core.telegram_bot import TelegramBot
from trading_bot.core.intelligent_order_replacement import IntelligentOrderReplacementSystem
from trading_bot.core.enhanced_position_monitor import EnhancedPositionMonitor
from trading_bot.core.telegram_monitor import TelegramMonitor
from trading_bot.core.rebuild_sync_manager import RebuildSyncManager, SyncConfig # Import RebuildSyncManager and SyncConfig
from trading_bot.core.incremental_sync_manager import IncrementalSyncManager # Import new incremental sync manager
from trading_bot.core.bybit_api_manager import BybitAPIManager
from trading_bot.core.slot_manager import SlotManager
from trading_bot.core.utils import (
    calculate_position_sizes_for_batch,
    create_workflow_state,
    update_workflow_state,
    create_signal_data,
    create_trade_data,
    smart_format_price,
    extract_symbol_from_filename,
    parse_timeframe_to_minutes,
    calculate_openai_pricing,
    get_utc_now,
    calculate_sleep_until_boundary,
    format_utc_time_for_display,
    count_open_positions_and_orders
)
from PIL import Image
from trading_bot.core.timeframe_extractor import TimeframeExtractor

# Optional imports
try:
    import psutil
except ImportError:
    psutil = None

# Global shutdown flag for graceful shutdown
shutdown_requested = False


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    global shutdown_requested
    logger.info(f"\n🛑 Shutdown signal received ({signum}). Finishing current cycle...")
    shutdown_requested = True


def log_step_header(step_name: str, step_num: Optional[int] = None):
    """Standardized step header logging."""
    header = f"🔄 STEP {step_num}: {step_name}" if step_num else f"🔄 {step_name}"
    logger.info(f"\n{'=' * 50}")
    logger.info(header)
    logger.info(f"{'=' * 50}")




def create_cycle_summary(processed: int, confirmed: int, executed: int, dry_run: bool) -> str:
    """Create standardized cycle completion summary."""
    summary = f"""
{'=' * 60}
SIMPLIFIED WORKFLOW CYCLE COMPLETE
{'=' * 60}
📊 Processed: {processed} signal(s)
✅ Confirmed: {confirmed}
🚀 Executed: {executed} trade(s)"""
    if dry_run:
        summary += "\n🔄 Mode: DRY RUN (no actual trades executed)"
    summary += f"\n{'=' * 60}"
    return summary


class SimpleTradingBot:
    """Optimized trading bot for standalone usage."""

    def __init__(self, use_testnet: bool = False):
        self.config = Config.from_yaml()
        self.use_testnet = use_testnet

        # Validate essential configuration
        if not self.config.openai.api_key:
            raise ValueError("OpenAI API key not found in config.yaml")

        # Initialize core components first
        self._init_core_components()

        # Initialize trading components
        self._init_trading_components()

        # Initialize monitoring components
        self._init_monitoring_components()

        # Initialize caching
        self._init_cache()

    def _init_core_components(self):
        """Initialize core components (API clients, data access)."""
        self.client: openai.OpenAI = openai.OpenAI(api_key=self.config.openai.api_key)
        self.api_manager = BybitAPIManager(self.config, use_testnet=self.use_testnet)
        self.data_agent = DataAgent()
        self.db_queue = DatabaseQueue(self.data_agent)

    def _init_trading_components(self):
        """Initialize trading-related components."""
        # Chart processing components
        self.sourcer = ChartSourcer(self.config)
        self.analyzer = ChartAnalyzer(self.client, self.config, api_manager=self.api_manager, logger=logger)
        self.validator = SignalValidator(self.config)

        # Chart cleaning with optimized config
        chart_config = self.config.file_management.chart_cleaning
        self.cleaner = ChartCleaner(
            self.data_agent,
            enable_backup=self.config.file_management.enable_backup,
            enable_age_based_cleaning=chart_config.enable_age_based_cleaning if chart_config else False,
            max_file_age_hours=chart_config.max_file_age_hours if chart_config else 24,
            enable_cycle_based_cleaning=chart_config.enable_cycle_based_cleaning if chart_config else True
        )

        # Trading execution components
        self.trader = TradeExecutor(self.config, use_testnet=self.use_testnet, api_manager=self.api_manager)
        self.position_manager = PositionManager(self.data_agent, self.trader)
        self.slot_manager = SlotManager(self.trader, self.data_agent, self.config)
        self.risk_manager = RiskManager(self.trader, self.position_manager, self.config, slot_manager=self.slot_manager)

        # Set cross-references
        self.trader.position_manager = self.position_manager
        self.trader.risk_manager = self.risk_manager

    def _init_monitoring_components(self):
        """Initialize monitoring and recommendation components."""
        # Sync configuration
        self.sync_config = SyncConfig(
            enabled=True,
            sync_interval_seconds=30,
            max_retries=3,
            rate_limit_requests_per_second=5,
            batch_size=50,
            error_recovery_delay_seconds=60,
            incremental_sync_enabled=True,
            full_sync_interval_minutes=5
        )

        # Legacy rebuild sync manager (kept for fallback only)
        self.rebuild_and_sync_manager = RebuildSyncManager(
            self.data_agent, self.trader, self.sync_config, api_manager=self.api_manager
        )

        # Initialize incremental sync manager for efficient real-time updates
        if hasattr(self.position_manager, 'trade_tracker'):
            self.incremental_sync_manager = IncrementalSyncManager(
                self.data_agent, self.api_manager, self.position_manager.trade_tracker
            )
            logger.info("✅ Incremental sync manager initialized for real-time trade tracking")
        else:
            self.incremental_sync_manager = None
            logger.warning("⚠️ Cannot initialize incremental sync - trade tracker not available")
        self.recommender = Recommender(self.db_queue, self.trader, self.position_manager, self.config)
        self.recommendation_service = RecommendationService(
            self.data_agent, self.recommender, TimestampValidator(), self.risk_manager
        )
        self.position_analyzer = PositionAnalyzer(self.trader)
        self.position_monitor = EnhancedPositionMonitor(
            self.trader, self.config,
            logger=logging.getLogger('trading_bot.core.enhanced_position_monitor'),
            data_agent=self.data_agent
        )

    def _init_cache(self):
        """Initialize caching system."""
        self._position_cache = None
        self._position_cache_timestamp = None
        self._position_cache_ttl = 60  # Increased from 30 to 60 seconds for better efficiency
        self._last_config_check = time.time()  # Initialize config check timestamp

    def _print_workflow_header(self, title: str, dry_run: bool = False):
        """Print a formatted header for workflow steps."""
        logger.info(f"\n{'=' * 60}")
        logger.info(f"{title}")
        logger.info(f"{'=' * 60}")
        if dry_run:
            logger.info("🔄 DRY RUN MODE - No actual trades will be executed")
        logger.info("")

    def reload_config(self, force: bool = False):
        """Reload configuration from file only if needed."""
        try:
            # Only reload if forced or if config file has been modified
            if not force and hasattr(self, '_last_config_check'):
                config_path = Path("config.yaml")
                if config_path.exists():
                    current_mtime = config_path.stat().st_mtime
                    if current_mtime <= self._last_config_check:
                        return  # Config hasn't changed, skip reload

            old_config = getattr(self, 'config', None)
            self.config = Config.from_yaml()
            self._last_config_check = time.time()

            # Only update components if config actually changed
            if old_config is None or self._config_changed(old_config, self.config):
                self._update_components_config()
                logger.info("🔄 Configuration reloaded and components updated")

        except Exception as e:
            logger.warning(f"⚠️ Warning: Failed to reload config: {e}")

    def _config_changed(self, old_config, new_config) -> bool:
        """Check if relevant config sections have changed."""
        # Compare key trading parameters that would affect bot behavior
        try:
            old_trading = old_config.trading
            new_trading = new_config.trading
            return (
                old_trading.risk_percentage != new_trading.risk_percentage or
                old_trading.max_concurrent_trades != new_trading.max_concurrent_trades or
                old_trading.min_confidence_threshold != new_trading.min_confidence_threshold or
                old_trading.min_rr != new_trading.min_rr
            )
        except:
            return True  # If comparison fails, assume config changed

    def _update_components_config(self):
        """Update component configurations efficiently."""
        if hasattr(self, 'trader') and self.trader:
            self.trader.config = self.config
        if hasattr(self, 'risk_manager') and self.risk_manager:
            self.risk_manager.config = self.config

    def get_cached_positions(self, force_refresh: bool = False):
        """Get cached positions to avoid repeated API calls.

        Args:
            force_refresh: If True, bypass cache and fetch fresh data

        Returns:
            tuple: (actual_positions, open_positions_dict)
                - actual_positions: list of position dictionaries from exchange
                - open_positions_dict: dict mapping symbol -> position for quick lookup
        """
        current_time = time.time()

        # Check if cache is valid and not forced refresh
        if (not force_refresh and
            self._position_cache is not None and
            self._position_cache_timestamp is not None and
            (current_time - self._position_cache_timestamp) < self._position_cache_ttl):
            return self._position_cache

        # Cache miss, expired, or forced refresh - fetch fresh data
        return self._fetch_fresh_positions(current_time)

    def _fetch_fresh_positions(self, current_time: float):
        """Fetch fresh position data from exchange."""
        try:
            positions_response = self.trader.api_manager.get_positions()
            if positions_response.get("retCode") == 0:
                actual_positions = positions_response.get("result", {}).get("list", [])
                # Create open positions dictionary for quick lookup
                open_positions = {
                    pos.get('symbol'): pos
                    for pos in actual_positions
                    if float(pos.get('size', 0)) > 0
                }

                self._position_cache = (actual_positions, open_positions)
                self._position_cache_timestamp = current_time

                if len(open_positions) > 0:
                    logger.info(f"🔍 Cached {len(open_positions)} open positions")

                return self._position_cache
            else:
                logger.warning(f"⚠️ Failed to fetch positions: {positions_response.get('retMsg', 'Unknown error')}")
                return ([], {})
        except Exception as e:
            logger.warning(f"⚠️ Error fetching positions: {e}")
            return ([], {})

    def invalidate_position_cache(self):
        """Invalidate the position cache to force fresh data on next fetch."""
        self._position_cache = None
        self._position_cache_timestamp = None

    def get_position_count_cached(self) -> int:
        """Get count of open positions using cache."""
        _, open_positions = self.get_cached_positions()
        return len(open_positions)

    def has_open_position_cached(self, symbol: str) -> bool:
        """Check if symbol has open position using cache."""
        _, open_positions = self.get_cached_positions()
        return symbol in open_positions or f"{symbol}.P" in open_positions


    def sanitize_filename(self, image_path: str) -> str:
        """Remove spaces from filename and return sanitized path."""
        path = Path(image_path)
        if ' ' in path.name:
            new_name = path.name.replace(' ', '')
            new_path = path.parent / new_name
            if path.exists() and not new_path.exists():
                path.rename(new_path)
            return str(new_path)
        return image_path

    def extract_symbol(self, filename: str) -> str:
        """Extract symbol from filename."""
        return extract_symbol_from_filename(filename)


    def calculate_pricing(self, prompt_tokens: int, completion_tokens: int) -> dict:
        """Calculate OpenAI pricing based on token usage."""
        return calculate_openai_pricing(prompt_tokens, completion_tokens)

    def parse_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe string to minutes."""
        return parse_timeframe_to_minutes(timeframe)

    def analyze_chart(self, image_path: str, symbol: str = "UNKNOWN", target_timeframe: Optional[str] = None) -> dict:
        """Analyze a single chart image and store result."""
        try:
            clean_path = self.sanitize_filename(image_path)

            if symbol == "UNKNOWN":
                symbol = self.extract_symbol(clean_path)

            analysis = self.analyzer.analyze_chart(clean_path, target_timeframe=target_timeframe)

            if analysis.get("skipped"):
                return analysis

            if analysis.get("error"):
                error_details = {
                    "image": clean_path,
                    "symbol": symbol,
                    "error": True,
                    "error_message": analysis.get("summary", "Unknown error"),
                    "error_type": "analysis_failed",
                    "raw_response": analysis.get("raw_response", ""),
                    "cost_usd": 0.0,
                    "tokens": 0
                }

                if "raw_response" in analysis:
                    error_details["raw_response"] = str(analysis["raw_response"])[:500] + "..." if len(
                        str(analysis["raw_response"])) > 500 else str(analysis["raw_response"])

                return error_details

            timeframe = analysis.get("extracted_timeframe")
            if not timeframe:
                logger.info(f"⚠️  WARNING: No timeframe found in analysis result for {symbol} - this should not happen")
                return {
                    "image": clean_path,
                    "symbol": symbol,
                    "error": True,
                    "error_message": f"Missing timeframe in analysis result for {symbol}",
                    "skipped": True,
                    "skip_reason": "missing_timeframe_in_result"
                }

            validation = self.validator.validate_signal(analysis)

            total_tokens = 1200
            pricing = self.calculate_pricing(1000, 200)

            result = {
                "image": clean_path,
                "symbol": symbol,
                "timeframe": timeframe,
                "analysis": analysis,
                "validation": validation,
                "recommendation": analysis["recommendation"] if validation["valid"] else "hold",
                "confidence": validation["adjusted_confidence"],
                "cost_usd": pricing["total_cost_usd"],
                "tokens": total_tokens
            }

            self.db_queue.enqueue(self.data_agent.store_result, symbol, timeframe, result, clean_path, analysis.get('analysis_prompt', ''))

            return result

        except Exception as e:
            return {
                "image": image_path,
                "symbol": symbol,
                "error": True,
                "error_message": str(e),
                "error_type": "exception",
                "cost_usd": 0.0,
                "tokens": 0
            }

    def analyze_all_images(self, folder_path: str, target_timeframe: Optional[str] = None) -> list:
        """Analyze all images in a folder and store results. Optionally filter by timeframe."""
        folder = Path(folder_path)
        results = []

        # Import the timeframe extraction function
        from trading_bot.core.utils import extract_timeframe_from_filename

        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.webp'}
        all_image_files = [f for f in folder.iterdir() if f.suffix.lower() in image_extensions]

        # Filter by timeframe if specified
        if target_timeframe:
            image_files = []
            for f in all_image_files:
                file_timeframe = extract_timeframe_from_filename(str(f))
                if file_timeframe and file_timeframe == target_timeframe:
                    image_files.append(f)
            logger.info(
                f"🔍 Filtered to {len(image_files)} images matching timeframe '{target_timeframe}' (out of {len(all_image_files)} total)")
        else:
            image_files = all_image_files

        total_files = len(image_files)
        logger.info(f"\n{'=' * 60}")
        if target_timeframe:
            logger.info(f"ANALYZING {total_files} IMAGES (filtered by '{target_timeframe}' timeframe)")
        else:
            logger.info(f"ANALYZING {total_files} IMAGES")
        logger.info(f"{'=' * 60}")

        # Process images in parallel using ThreadPoolExecutor
        from concurrent.futures import ThreadPoolExecutor, as_completed

        # Limit concurrent threads to avoid overwhelming the API
        max_workers = min(5, max(1, total_files))  # Up to 5 concurrent analyses

        def process_single_image(image_file, index):
            """Process a single image and return result with index for ordering."""
            try:
                symbol = self.extract_symbol(str(image_file))

                logger.info(
                    f"[{index + 1:2d}/{total_files}]{format_utc_time_for_display(get_utc_now())} ANALYZING: {image_file}")
                logger.info(f"         Symbol: {symbol}")

                result = self.analyze_chart(str(image_file), symbol, target_timeframe)

                if result.get("skipped"):
                    logger.info(f"         SKIPPED: Already analyzed.")
                    return (index, result)

                if "error" in result and result["error"]:
                    logger.info(f"         ERROR: {result['error_message']}")
                    if result.get("raw_response"):
                        logger.info(f"         Raw Response: {result['raw_response']}")
                    logger.info(f"         Cost: $0.00 (failed)")
                    return (index, result)
                else:
                    cost = result.get("cost_usd", 0.0)
                    recommendation = result['recommendation'].lower()
                    if recommendation == 'buy':
                        emoji = "📈"
                    elif recommendation == 'sell':
                        emoji = "📉"
                    else:
                        emoji = "⏸️"

                    logger.info(f"         Result: {emoji} {result['recommendation']} (confidence: {result['confidence']})")
                    logger.info(f"         Cost: ${cost:.4f}")
                    return (index, result)

            except Exception as e:
                logger.info(f"[{index + 1:2d}/{total_files}] ERROR: {image_file.name}")
                logger.info(f"         Error: {str(e)}")
                logger.info(f"         Cost: $0.00 (exception)")
                return (index, {
                    "image": str(image_file),
                    "error": True,
                    "error_message": str(e),
                    "error_type": "exception",
                    "cost_usd": 0.0,
                    "tokens": 0
                })

        # Execute parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_index = {
                executor.submit(process_single_image, image_file, i): i
                for i, image_file in enumerate(image_files)
            }

            # Collect results in order
            ordered_results = [None] * total_files
            skipped_count = 0
            total_cost = 0.0

            for future in as_completed(future_to_index):
                try:
                    index, result = future.result()
                    ordered_results[index] = result

                    # Update counters
                    if result.get("skipped"):
                        skipped_count += 1
                    elif not result.get("error", False):
                        total_cost += result.get("cost_usd", 0.0)
                except Exception as e:
                    logger.info(f"❌ Error collecting result: {e}")

        # Filter out None results and create final results list
        results = [r for r in ordered_results if r is not None]

        logger.info(f"\n{'=' * 60}")
        logger.info(f"ANALYSIS COMPLETE")
        logger.info(f"{'=' * 60}")
        logger.info(f"Total files: {total_files}")
        logger.info(f"Skipped: {skipped_count} (already analyzed)")
        logger.info(f"New analyses: {total_files - skipped_count}")
        logger.info(f"Total cost: ${total_cost:.4f}")
        logger.info(f"{'=' * 60}")

        return results

    def is_recommendation_valid(self, recommendation: dict) -> bool:
        """
        Check if recommendation is valid using the Recommender's validation logic.
        This includes both timestamp and data validation.
        """
        if not self.recommender:
            return False
        return self.recommender.is_recommendation_valid(recommendation)

    def _get_skip_icon(self, reason: str) -> str:
        """Get appropriate icon for skip case based on reason text."""
        reason_lower = reason.lower()

        if 'risk-reward ratio' in reason_lower or 'below minimum' in reason_lower:
            return "📊"

        if any(keyword in reason_lower for keyword in
               ['balance', 'usdt', 'position sizing failed', 'no usdt available']):
            return "💰"

        if 'recommendation is' in reason_lower and 'hold' in reason_lower:
            return "🤚"

        if 'open position already exists' in reason_lower or 'position already exists' in reason_lower:
            return "🔄"

        if 'confidence' in reason_lower and (
                'below' in reason_lower or 'threshold' in reason_lower or 'invalid' in reason_lower):
            return "📉"

        return "⏸️"

    def _format_trading_output(self, icon: str, symbol: str, timeframe: str, action: str, reason: str,
                               error_details: Optional[str] = None, api_info: Optional[str] = None) -> None:
        """Helper function for consistent trading output formatting."""
        if action.upper() == "SKIP":
            icon = self._get_skip_icon(reason)

        symbol_part = f"{icon} {symbol} ({timeframe})"
        action_part = action.upper()
        logger.info(f"{symbol_part:<22} │ {action_part:<7} │ {reason}")

        if error_details:
            logger.info(f"   └─ Details: {error_details}")
        if api_info:
            logger.info(f"   └─ API: {api_info}")

    def execute_valid_recommendations(self, symbols: Optional[list] = None, dry_run: bool = False,
                                      target_timeframe: Optional[str] = None) -> dict:
        """Execute trades for valid recommendations across multiple timeframes."""
        results = []
        symbol_list = symbols or []
        skip_reasons = {}

        def _track_skip_reason(reason: str):
            skip_reasons[reason] = skip_reasons.get(reason, 0) + 1

        recommendations_to_process = []

        if target_timeframe:
            logger.info(f"🎯 Autotrader mode: Only checking {target_timeframe} timeframe")

            logger.info("📊 Getting latest recommendations...")

            all_recommendations = self.recommender.get_latest_recommendation("all", target_timeframe)

            if all_recommendations and isinstance(all_recommendations, list):
                if symbol_list:
                    for rec in all_recommendations:
                        if isinstance(rec, dict) and rec.get('symbol') in symbol_list:
                            recommendations_to_process.append(rec)
                else:
                    recommendations_to_process = all_recommendations
            else:
                # Handle cases where no recommendations are found or an error occurred
                if symbol_list:
                    all_analysis = self.data_agent.get_all_latest_analysis()
                    timeframe_analysis = [rec for rec in all_analysis if rec.get('timeframe') == target_timeframe] if all_analysis else []

                    if not all_analysis:
                        for symbol in symbol_list:
                            _track_skip_reason("No analysis data in database")
                    elif not timeframe_analysis:
                        for symbol in symbol_list:
                            _track_skip_reason(f"No {target_timeframe} analysis in database")
                    else:
                        for symbol in symbol_list:
                            analysis = [rec for rec in timeframe_analysis if rec.get('symbol') == symbol]
                            if not analysis:
                                _track_skip_reason(f"No {target_timeframe} analysis for {symbol}")
                            else:
                                analysis_data = analysis[0] # Get the first analysis data
                                rec_type = analysis_data.get('recommendation', '').upper()
                                if rec_type == 'HOLD':
                                    _track_skip_reason("HOLD")
                                elif not rec_type or rec_type not in ['BUY', 'SELL']:
                                    _track_skip_reason("Invalid recommendation type")
                                elif not self.is_recommendation_valid(analysis_data): # Pass analysis_data
                                    _track_skip_reason("Stale recommendation")
                                else:
                                    _track_skip_reason("Filtered by recommender")
        else:
            timeframes_to_check = ["15m", "1h", "4h", "1d"]
            for symbol in symbol_list:
                for timeframe in timeframes_to_check:
                    recommendation = self.recommender.get_latest_recommendation(symbol, timeframe)

                    if not recommendation or isinstance(recommendation, list):
                        analysis = self.data_agent.get_latest_analysis(symbol, timeframe)
                        if not analysis:
                            _track_skip_reason(f"No {timeframe} analysis for {symbol}")
                        else:
                            rec_type = analysis.get('recommendation', '').upper()
                            if rec_type == 'HOLD':
                                _track_skip_reason("HOLD")
                            elif not rec_type or rec_type not in ['BUY', 'SELL']:
                                _track_skip_reason("Invalid recommendation type")
                            elif not self.is_recommendation_valid(analysis):
                                _track_skip_reason("Stale recommendation")
                            else:
                                _track_skip_reason("Filtered by recommender")
                        continue

                    if isinstance(recommendation, dict) and recommendation.get('error'):
                        error_msg = recommendation.get('error', 'Unknown error')
                        if 'Risk validation failed' in error_msg:
                            clean_reason = error_msg.replace('Risk validation failed: ', '')
                            self._format_trading_output("", symbol, timeframe, "SKIP", clean_reason)
                            _track_skip_reason(f"Risk validation: {clean_reason}")
                        else:
                            self._format_trading_output("", symbol, timeframe, "SKIP", error_msg)
                            _track_skip_reason(f"Error: {error_msg}")
                        continue

                    recommendations_to_process.append(recommendation)

        try:
            # Use the utility function to get position and order counts
            position_order_data = count_open_positions_and_orders(trader=self.trader)
            actual_positions = position_order_data.get("raw_positions", [])
            open_positions = {pos.get('symbol'): pos for pos in actual_positions if float(pos.get('size', 0)) > 0}
        except Exception as e:
            logger.info(f"⚠️ Warning: Failed to get position and order data: {e}")
            actual_positions, open_positions = [], {}

        for recommendation in recommendations_to_process:
            if not isinstance(recommendation, dict):
                continue

            symbol = recommendation.get('symbol')
            timeframe = recommendation.get('timeframe')
            rec_type = recommendation.get('recommendation', '').upper()

            if not symbol or not timeframe:
                _track_skip_reason("Missing symbol or timeframe")
                continue

            if not self.is_recommendation_valid(recommendation):
                self._format_trading_output("", symbol, timeframe, "SKIP", "Stale recommendation")
                _track_skip_reason("Stale recommendation")
                continue

            if rec_type not in ['BUY', 'SELL', 'LONG', 'SHORT']:
                reason = f"Invalid recommendation type: '{rec_type}'" if rec_type else "No recommendation type"
                self._format_trading_output("", symbol, timeframe, "SKIP", reason)
                if rec_type == 'HOLD':
                    _track_skip_reason("HOLD")
                else:
                    _track_skip_reason("Invalid recommendation")
                continue

            if symbol in open_positions:
                self._format_trading_output("", symbol, timeframe, "SKIP", "open position already exists")
                _track_skip_reason("Position already exists")
                continue

            try:
                confidence = float(recommendation.get('confidence', 0))
            except (ValueError, TypeError):
                self._format_trading_output("", symbol, timeframe, "SKIP", "invalid confidence value")
                _track_skip_reason("Invalid confidence value")
                continue

            trade_signal = {
                "recommendation": str(recommendation.get('recommendation', '')),
                "entry_price": float(recommendation.get('entry_price', 0)),
                "take_profit": float(recommendation.get('take_profit', 0)),
                "stop_loss": float(recommendation.get('stop_loss', 0)),
                "direction": str(recommendation.get('direction', '')),
                "confidence": confidence,
                "timeframe": timeframe,
                "recommendation_id": str(recommendation.get('id', ''))
            }

            result = self.trader.execute_trade(
                signal=trade_signal,
                symbol=symbol,
                order_type="Limit",
                dry_run=dry_run
            )

            # Check if trade was rejected and provide feedback
            if result.get('status') == 'rejected':
                error_msg = result.get('error', 'Unknown error')
                # Check if it's a complex error that needs breakdown
                if 'Failed to get instrument info' in error_msg and 'ErrCode:' in error_msg:
                    # Extract main error and details
                    if 'ErrCode:' in error_msg:
                        main_error = error_msg.split('ErrCode:')[0].strip()
                        error_code = 'ErrCode:' + error_msg.split('ErrCode:')[1].split('.')[0]
                        details = error_msg.split('Failed to get instrument info for ')[1].split(':')[
                            0] if 'Failed to get instrument info for ' in error_msg else None
                        api_call = error_msg.split('Request → ')[1] if 'Request → ' in error_msg else None

                        self._format_trading_output("❌", symbol, timeframe, "ERROR",
                                                    f"{main_error.split(':')[-1].strip()} ({error_code})", details,
                                                    api_call)
                        _track_skip_reason(f"API Error: {error_code}")
                    else:
                        self._format_trading_output("", symbol, timeframe, "SKIP", error_msg)
                        _track_skip_reason(f"Rejected: {error_msg}")
                else:
                    self._format_trading_output("", symbol, timeframe, "SKIP", error_msg)
                    _track_skip_reason(f"Rejected: {error_msg}")
            elif result.get('status') == 'dry_run':
                self._format_trading_output("📋", symbol, timeframe, "EXECUTE",
                                            f"DRY RUN: {recommendation.get('recommendation')} trade")
            elif result.get('status') == 'failed':
                self._format_trading_output("❌", symbol, timeframe, "ERROR", result.get('error', 'Unknown error'))
            else:
                self._format_trading_output("🚀", symbol, timeframe, "EXECUTE",
                                            f"{recommendation.get('recommendation')} trade")

            results.append({
                "symbol": symbol,
                "recommendation": recommendation,
                "trade_result": result,
                "timeframe": timeframe  # Add timeframe to results for debugging
            })

        return {
            "results": results,
            "skip_reasons": skip_reasons
        }

    def check_recent_recommendations(self, timeframe: str) -> bool:
        """Check if fresh recommendations exist for the given timeframe using boundary-aware method.
        
        Args:
            timeframe: Timeframe string (e.g., "4h", "1h", "30m")
            
        Returns:
            bool: True if fresh recommendations exist, False if screenshots needed
        """
        try:
            # Use the recommendation service for consistent checking
            return self.recommendation_service.is_fresh_data_available(timeframe)
        except Exception as e:
            logger.info(f"❌ Error checking recent recommendations: {e}")
            return False

    def get_fresh_recommendations_for_processing(self, timeframe: str, open_positions=None) -> list:
        """Get fresh recommendations that should be processed for trading.
        
        Args:
            timeframe: Timeframe string (e.g., "4h", "1h", "30m")
            open_positions: Optional list of open positions from batch check to avoid individual API calls
            
        Returns:
            List of trade info dictionaries for fresh recommendations ready for processing
        """
        try:
            # Get all latest analysis from database
            all_analysis = self.data_agent.get_all_latest_analysis()

            if not all_analysis:
                return []

            # Filter analysis to only include the requested timeframe
            matching_timeframe_analysis = [
                analysis for analysis in all_analysis
                if analysis.get('timeframe') == timeframe
            ]

            if not matching_timeframe_analysis:
                return []

            fresh_trades = []

            for analysis in matching_timeframe_analysis:
                # Use the original recommendation with its actual timeframe
                recommendation = {
                    'timestamp': analysis.get('timestamp'),
                    'timeframe': analysis.get('timeframe'),
                    'symbol': analysis.get('symbol'),
                    'recommendation': analysis.get('recommendation')
                }

                # Check if this recommendation is still valid
                if self.is_recommendation_valid(recommendation):
                    symbol = analysis.get('symbol')
                    recommendation_type = analysis.get('recommendation', '').upper()

                    # Skip if symbol is missing
                    if not symbol:
                        continue

                    # Check if this is a valid trading recommendation
                    if recommendation_type in ['BUY', 'SELL', 'LONG', 'SHORT']:
                        # Check if we already have an open position for this symbol
                        if open_positions is not None:
                            # Use batch position data to avoid individual API calls
                            has_position = open_positions.get(symbol, False) or open_positions.get(f"{symbol}.P", False)
                        else:
                            # Use cached position check instead of individual API call
                            has_position = self.has_open_position_cached(symbol)

                        if not has_position:
                            # Check confidence threshold
                            confidence = float(analysis.get('confidence', 0))
                            if confidence >= self.config.trading.min_confidence_threshold:
                                fresh_trades.append({
                                    "symbol": symbol,
                                    "recommendation": analysis
                                })
                                logger.info(
                                    f"✅ Found fresh trade for {symbol}: {recommendation_type} (confidence: {confidence})")
                            else:
                                logger.info(f"⏸️ Skipping {symbol}: confidence {confidence} below threshold")
                        else:
                            logger.info(f"⏸️ Skipping {symbol}: open position already exists")
                    else:
                        logger.info(f"⏸️ Skipping {symbol}: recommendation is '{recommendation_type}'")

            return fresh_trades

        except Exception as e:
            logger.info(f"❌ Error getting fresh recommendations for processing: {e}")
            return []

    def check_current_cycle_recommendations(self, timeframe: str, current_time: datetime, open_positions=None) -> list:
        """
        Check for recommendations that belong to the current cycle and haven't been traded yet.
        
        For mid-cycle starts (e.g., starting at 1:15 for 1h timeframe), this method will
        process recommendations from the previous boundary to allow processing of the
        partial cycle that occurred before the autotrader started.
        
        Args:
            timeframe: Target timeframe (e.g., "15m", "1h", "4h")
            current_time: Current UTC time
            
        Returns:
            List of trade info dictionaries for pending trades in current cycle
        """
        try:
            # Initialize timestamp validator for boundary calculations
            validator = TimestampValidator()

            # Calculate the current cycle boundaries
            timeframe_minutes = self.parse_timeframe_minutes(timeframe)

            # Calculate current cycle start boundary
            if timeframe == "1d":
                # Daily boundary: current day at 00:00 UTC
                current_boundary = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
            elif timeframe_minutes >= 60:
                # Hourly boundaries: align to hour boundaries
                hours = timeframe_minutes // 60
                current_hour = current_time.hour
                cycle_hour = (current_hour // hours) * hours
                current_boundary = current_time.replace(hour=cycle_hour, minute=0, second=0, microsecond=0)
            else:
                # Minute boundaries: align to minute boundaries
                current_minute = current_time.hour * 60 + current_time.minute
                cycle_minute = (current_minute // timeframe_minutes) * timeframe_minutes
                hour = cycle_minute // 60
                minute = cycle_minute % 60
                current_boundary = current_time.replace(hour=hour, minute=minute, second=0, microsecond=0)

            # Check if we're starting mid-cycle (current time is not exactly at boundary)
            is_mid_cycle_start = current_time > current_boundary

            if is_mid_cycle_start:
                # For mid-cycle start, process the previous complete cycle
                # This allows processing recommendations from the previous boundary
                if timeframe == "1d":
                    cycle_start = current_boundary - timedelta(days=1)
                    cycle_end = current_boundary
                elif timeframe_minutes >= 60:
                    hours = timeframe_minutes // 60
                    cycle_start = current_boundary - timedelta(hours=hours)
                    cycle_end = current_boundary
                else:
                    cycle_start = current_boundary - timedelta(minutes=timeframe_minutes)
                    cycle_end = current_boundary

            else:
                # Normal cycle start (exactly at boundary)
                cycle_start = current_boundary
                cycle_end = validator.calculate_next_boundary(cycle_start, timeframe)
            # Get all latest analysis from database
            all_analysis = self.data_agent.get_all_latest_analysis()

            if not all_analysis:
                return []

            # Filter analysis to only include the requested timeframe
            matching_timeframe_analysis = [
                analysis for analysis in all_analysis
                if analysis.get('timeframe') == timeframe
            ]

            if not matching_timeframe_analysis:
                return []

            current_cycle_trades = []

            for analysis in matching_timeframe_analysis:
                try:
                    # Parse the recommendation timestamp
                    timestamp_str = analysis.get('timestamp')
                    if not timestamp_str:
                        continue

                    rec_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    if rec_time.tzinfo is None:
                        rec_time = rec_time.replace(tzinfo=timezone.utc)

                    # Check if this recommendation belongs to the current cycle
                    if cycle_start <= rec_time < cycle_end:
                        symbol = analysis.get('symbol')
                        recommendation_type = analysis.get('recommendation', '').upper()

                        if not symbol:
                            continue

                        if recommendation_type in ['BUY', 'SELL', 'LONG', 'SHORT']:
                            has_position = False
                            if open_positions is not None:
                                # Use batch position data to avoid individual API calls
                                # open_positions is a dict with symbol -> bool mapping
                                has_position = open_positions.get(symbol, False) or open_positions.get(f"{symbol}.P",
                                                                                                       False)
                            else:
                                # Fallback to individual API call if batch data not provided
                                has_position = self.trader.has_open_position(symbol)

                            if not has_position:
                                confidence = float(analysis.get('confidence', 0))
                                if confidence >= self.config.trading.min_confidence_threshold:
                                    current_cycle_trades.append({
                                        "symbol": symbol,
                                        "recommendation": analysis,
                                        "cycle_start": cycle_start,
                                        "cycle_end": cycle_end
                                    })
                                    logger.info(
                                        f"✅ Found pending trade for {symbol}: {recommendation_type} (confidence: {confidence})")
                                else:
                                    logger.info(f"⏸️ Skipping {symbol}: confidence {confidence} below threshold")
                            else:
                                logger.info(f"⏸️ Skipping {symbol}: open position already exists")
                        else:
                            logger.info(f"⏸️ Skipping {symbol}: recommendation is '{recommendation_type}'")

                except Exception as e:
                    logger.info(f"⚠️ Error processing analysis for {analysis.get('symbol', 'unknown')}: {e}")
                    continue

            return current_cycle_trades

        except Exception as e:
            logger.info(f"❌ Error checking current cycle recommendations: {e}")
            return []

    def _process_intelligent_recommendations(self, timeframe: str, current_time: datetime) -> list:
        """
        Process recommendations through the intelligent workflow:
        Chart Analysis → Recommender Filtering → Intelligent Order Replacement → Batch Preparation
        
        Args:
            timeframe: Target timeframe (e.g., "15m", "1h", "4h")
            current_time: Current UTC time
            
        Returns:
            List of trade info dictionaries ready for telegram confirmation
        """
        try:
            logger.info(f"🧠 Processing intelligent recommendations for {timeframe}...")

            actual_positions, open_positions = self.get_cached_positions()
            unique_symbols = set(pos['symbol'] for pos in actual_positions)

            available_slots = self.risk_manager.get_available_slots() if self.risk_manager else 5
            logger.info(f"📊 Available trading slots: {available_slots}")

            if available_slots <= 0:
                logger.info(f"⏸️ No available slots - skipping recommendation processing")
                return []

            all_trades = []

            current_cycle_trades = self.check_current_cycle_recommendations(timeframe, current_time, open_positions)
            if current_cycle_trades:
                all_trades.extend(current_cycle_trades)
                logger.info(f"🎯 Found {len(current_cycle_trades)} midcycle recommendations for {timeframe}")

            fresh_trades = self.get_fresh_recommendations_for_processing(timeframe, open_positions)
            if fresh_trades:
                all_trades.extend(fresh_trades)
                logger.info(f"🎯 Found {len(fresh_trades)} fresh recommendations for {timeframe}")

            if not all_trades:
                logger.info(f"📊 No recommendations found for {timeframe} (midcycle or fresh)")
                return []

            logger.info(f"🎯 Found {len(all_trades)} total recommendations for {timeframe} cycle (midcycle + fresh)")

            if self.recommender and hasattr(self.recommender,
                                            'intelligent_replacement') and self.recommender.intelligent_replacement:
                logger.info(f"🧠 Processing through intelligent order replacement system...")

                for trade_info in all_trades:
                    recommendation = trade_info["recommendation"]

                    signal_data = {
                        'symbol': trade_info["symbol"],
                        'recommendation': recommendation.get("recommendation", ""),
                        'entry_price': recommendation.get("entry_price", 0),
                        'take_profit': recommendation.get("take_profit", 0),
                        'stop_loss': recommendation.get("stop_loss", 0),
                        'direction': recommendation.get("direction", ""),
                        'confidence': recommendation.get("confidence", 0),
                        'timeframe': timeframe,
                        'recommendation_id': recommendation.get("id", ""),
                        'timestamp': recommendation.get("timestamp", current_time.isoformat())
                    }

                    self.recommender.intelligent_replacement.add_signal_for_evaluation(signal_data)

                intelligent_result = self.recommender.intelligent_replacement.process_intelligent_recommendations(
                    available_slots)

                logger.info(f"🧠 Intelligent processing result: {intelligent_result.get('action')}")

                if intelligent_result.get('action') == 'execute_all':
                    recommended_signals = intelligent_result.get('recommended_signals', [])
                    logger.info(f"✅ All {len(recommended_signals)} signals approved for execution")

                    processed_trades = []
                    for signal in recommended_signals:
                        for trade_info in all_trades:
                            if trade_info["symbol"] == signal.get('symbol'):
                                processed_trades.append(trade_info)
                                break

                    return processed_trades

                elif intelligent_result.get('action') == 'replace_and_execute':
                    recommended_signals = intelligent_result.get('recommended_signals', [])
                    replacement_info = intelligent_result.get('replacements', {})

                    successful_replacements = replacement_info.get('successful_replacements', 0)
                    logger.info(f"🔄 Executed {successful_replacements} order replacements")
                    logger.info(f"✅ {len(recommended_signals)} signals approved for execution after replacements")

                    processed_trades = []
                    for signal in recommended_signals:
                        for trade_info in all_trades:
                            if trade_info["symbol"] == signal.get('symbol'):
                                processed_trades.append(trade_info)
                                break

                    return processed_trades

                elif intelligent_result.get('action') == 'skip_all':
                    reason = intelligent_result.get('message', 'All signals filtered by intelligent system')
                    logger.info(f"⏸️ All signals filtered: {reason}")
                    return []

                else:
                    error_msg = intelligent_result.get('error', 'Unknown intelligent processing result')
                    logger.info(f"❌ Intelligent processing error: {error_msg}")
                    return all_trades

            else:
                logger.info(f"⚠️ Intelligent order replacement system not available - using basic recommendations")
                return all_trades

        except Exception as e:
            logger.info(f"❌ Error in intelligent recommendation processing: {e}")
            try:
                return self.check_current_cycle_recommendations(timeframe, current_time)
            except Exception as fallback_error:
                logger.info(f"❌ Fallback also failed: {fallback_error}")
                return []

    async def get_charts(self, count: Optional[int] = None, timeframe_check: Optional[str] = None,
                         force_capture: bool = False) -> dict:
        """Capture TradingView watchlist screenshots with resource management."""
        if not self.sourcer.tradingview_enabled:
            return {
                "status": "error",
                "error": "TradingView automation not enabled in config"
            }

        # Check system resources before starting
        if psutil is not None:
            try:
                memory = psutil.virtual_memory()
                if memory.percent > 85:
                    logger.info(f"⚠️ High memory usage detected: {memory.percent:.1f}% - attempting cleanup")
                    import gc
                    gc.collect()
            except Exception:
                pass  # psutil not available or failed

        if timeframe_check and not force_capture:
            logger.info(f"\n{'=' * 60}")
            logger.info(f"CHECKING DATABASE FOR FRESH {timeframe_check.upper()} ANALYSIS")
            logger.info(f"{'=' * 60}")

            if self.check_recent_recommendations(timeframe_check):
                all_analysis = self.data_agent.get_all_latest_analysis()
                symbols = list(
                    set([analysis.get('symbol', 'UNKNOWN') for analysis in all_analysis if analysis.get('symbol')]))
                symbols.sort()
                symbols_str = ', '.join(symbols) if symbols else 'symbols'

                return {
                    "status": "success",
                    "message": f"Recommendations for {symbols_str} up to date, no screenshots needed",
                    "skipped": True,
                    "timeframe": timeframe_check,
                    "symbols_count": len(symbols)
                }
            else:
                logger.info(f"No fresh {timeframe_check} analysis found. Proceeding with screenshot capture...")
        elif timeframe_check and force_capture:
            logger.info(f"\n{'=' * 60}")
            logger.info(
                f"{format_utc_time_for_display(get_utc_now())} FORCING CHART CAPTURE FOR {timeframe_check.upper()} (MID-CYCLE)")
            logger.info(f"{'=' * 60}")
            logger.info(f"Mid-cycle logic determined stale recommendations - bypassing freshness check")

        try:
            logger.info(f"\n{'=' * 60}")
            logger.info("TRADINGVIEW CHART CAPTURE")
            logger.info(f"{'=' * 60}")

            try:
                from trading_bot.core.secrets_manager import get_tradingview_credentials
                current_user = get_tradingview_credentials()[0] or "Unknown"
            except ImportError:
                import os
                current_user = os.getenv("TRADINGVIEW_EMAIL", "Unknown")
            logger.info(f"User: {current_user}")

            if self.config.tradingview.target_chart:
                logger.info(f"Target Chart: {self.config.tradingview.target_chart}")
            logger.info(f"{'=' * 60}")

            logger.info("1. Setting up browser...")
            if not await self.sourcer.setup_browser_session():
                await self.sourcer.cleanup_browser_session()
                return {
                    "status": "error",
                    "error": "Browser setup failed"
                }
            logger.info("✓ Browser setup successful")

            logger.info("2. Authenticating...")
            auth_result = await self.sourcer.authenticate_tradingview()
            if not auth_result:
                await self.sourcer.cleanup_browser_session()
                logger.info("✓ Browser cleaned up after authentication failure")
                return {
                    "status": "error",
                    "error": "Authentication failed"
                }
            logger.info("✓ Authentication successful")

            logger.info("3. Discovering watchlist symbols...")
            # Pass already_authenticated=True since we just authenticated in step 2
            symbols = await self.sourcer.get_watchlist_symbols(already_authenticated=True)
            if not symbols:
                await self.sourcer.cleanup_browser_session()
                logger.info("✓ Browser cleaned up after symbol discovery failure")
                return {
                    "status": "error",
                    "error": "No symbols found in watchlist"
                }

            if count is not None and count > 0:
                symbols = symbols[:count]
                logger.info(f"✓ Found {len(symbols)} symbols (limited to first {count})")
            else:
                logger.info(f"✓ Found {len(symbols)} symbols")

            for i, symbol in enumerate(symbols, 1):
                logger.info(f"   {i:2d}. {symbol}")

            logger.info(f"\n4. Capturing screenshots...")
            screenshot_paths = await self.sourcer.capture_all_watchlist_screenshots(
                target_chart=self.config.tradingview.target_chart, timeframe=timeframe_check)

            if screenshot_paths:
                captured_count = len(screenshot_paths)
                logger.info(f"\n✅ SUCCESS! Captured {captured_count} screenshots")
                logger.info(f"📁 All files saved to: {self.config.paths.charts}/")

                if timeframe_check:
                    first_screenshot = next(iter(screenshot_paths.values()))
                    try:
                        img = Image.open(first_screenshot)
                        timeframe_cropped = img.crop((100, 0, 400, 70))
                        extracted_timeframe = TimeframeExtractor().extract_timeframe_from_image(timeframe_cropped)

                        if extracted_timeframe:
                            if extracted_timeframe != timeframe_check:
                                await self.sourcer.cleanup_browser_session()
                                logger.info(f"\n❌ CRITICAL ERROR: TIMEFRAME MISMATCH DETECTED!")
                                logger.info(f"❌ Requested timeframe: {timeframe_check}")
                                logger.info(f"❌ Actual chart timeframe: {extracted_timeframe}")
                                logger.info(
                                    f"❌ The TradingView chart is set to {extracted_timeframe} but autotrader expects {timeframe_check}")
                                logger.info(
                                    f"❌ Please change the TradingView chart timeframe to {timeframe_check} and restart the bot")
                                logger.info(f"❌ BOT TERMINATED TO PREVENT INCORRECT TRADING DECISIONS")

                                return {
                                    "status": "error",
                                    "error": f"Timeframe mismatch: requested {timeframe_check}, got {extracted_timeframe}",
                                    "timeframe_mismatch": True,
                                    "requested_timeframe": timeframe_check,
                                    "actual_timeframe": extracted_timeframe
                                }
                            else:
                                logger.info(f"✅ Timeframe validation passed: {extracted_timeframe} matches {timeframe_check}")
                        else:
                            logger.info(f"⚠️ Warning: Could not extract timeframe from chart - proceeding without validation")

                    except Exception as e:
                        logger.info(f"⚠️ Warning: Timeframe validation failed: {str(e)} - proceeding without validation")

                return {
                    "status": "success",
                    "captured_count": captured_count,
                    "total_symbols": len(symbols),
                    "screenshot_paths": screenshot_paths,
                    "success_rate": (captured_count / len(symbols)) * 100
                }
            else:
                await self.sourcer.cleanup_browser_session()
                logger.info("✓ Browser cleaned up after screenshot failure")
                return {
                    "status": "error",
                    "error": "No screenshots captured"
                }

        except Exception as e:
            await self.sourcer.cleanup_browser_session()
            logger.info("✓ Browser cleaned up due to error")
            return {
                "status": "error",
                "error": str(e)
            }

        await self.sourcer.cleanup_browser_session()
        logger.info("✓ Browser cleaned up")

    async def _sleep_with_shutdown_check(self, total_seconds: float) -> None:
        """Sleep for specified duration while checking for shutdown signal."""
        global shutdown_requested

        check_interval = 1 # Reduced for faster shutdown detection
        elapsed = 0

        while elapsed < total_seconds and not shutdown_requested:
            sleep_time = min(check_interval, total_seconds - elapsed)
            await asyncio.sleep(sleep_time)
            elapsed += sleep_time

            remaining = total_seconds - elapsed
            if remaining > 0 and int(remaining) % 300 == 0:
                logger.info(f"💤 {remaining / 60:.0f} minutes remaining until next cycle...")

    async def _run_position_monitor_background(self) -> None:
        """Run position monitor in background every 2 minutes."""
        monitor_interval = 120

        # Initialize Telegram monitor for position updates
        telegram_monitor = TelegramMonitor()

        while not shutdown_requested:
            try:
                logger.info("========================================")
                logger.info(f"POSITION MONITOR  {get_utc_now()}")
                logger.info("========================================")

                # Enhanced timestamp sync monitoring on every position monitor round
                sync_status = self.trader.api_manager.get_timestamp_sync_status()
                if sync_status.get("status") == "success":
                    sync_data = sync_status.get("sync_data", {})
                    offset_ms = sync_data.get("offset_ms", 0)
                    last_sync_ago = sync_data.get("last_sync_ago_seconds", 0)
                    
                    logger.info(f"🕐 Timestamp sync: Offset={offset_ms}ms, Last sync={last_sync_ago:.1f}s ago")
                    
                    # Auto-sync if needed (more than 60 seconds since last sync)
                    if last_sync_ago > 60:
                        logger.info(f"🔄 Auto-syncing timestamp (last sync was {last_sync_ago:.1f}s ago)...")
                        if self.trader.api_manager.force_timestamp_sync():
                            logger.info(f"✅ Auto timestamp sync successful")
                        else:
                            logger.info(f"⚠️ Auto timestamp sync failed")
                else:
                    logger.info(f"⚠️ Timestamp sync status error: {sync_status.get('error', 'Unknown error')}")

                # STEP 2: Monitor and tighten positions
                monitor_result = await self.position_monitor.monitor_positions()

                if monitor_result.get("status") == "success":
                    positions_checked = monitor_result.get("positions_checked", 0)
                    tightenings_applied = monitor_result.get("tightenings_applied", 0)

                    if tightenings_applied > 0:
                        logger.info(
                            f"✅ Position monitor: {tightenings_applied} stop losses tightened out of {positions_checked} positions")

                        for result in monitor_result.get("results", []):
                            if result.get("status") == "tightened":
                                symbol = result.get("symbol")
                                old_sl = result.get("old_stop_loss")
                                new_sl = result.get("new_stop_loss")
                                profit_r = result.get("profit_r")
                                tightening_level = result.get("tightening_level", "")
                                logger.info(
                                    f"   📈 {symbol} ({tightening_level}): SL {old_sl:.6f} -> {new_sl:.6f} (Profit: {profit_r:.2f}R)")
                    else:
                        logger.info(f"{format_utc_time_for_display(get_utc_now())}")
                        logger.info(f"\n📊 Position monitor: Positions checked {positions_checked}")
                        logger.info("--> NO tightenings needed".upper())

                    # Always display the positions table and profit summary
                    all_positions = monitor_result.get("all_positions", [])
                    if all_positions:
                        all_positions.sort(key=lambda x: x["profit_r"], reverse=True)
                        logger.info(f"\n   💰 Current profits: ")
                        profit_strs = [f"{pos['symbol']} {pos['profit_r']:.2f}R" for pos in all_positions]
                        logger.info(", ".join(profit_strs))

                        self.position_analyzer.display_positions_table()

                        # Send position update via Telegram (only when we have positions)
                        try:
                            # Use the utility function to get position and order counts
                            position_order_data = count_open_positions_and_orders(trader=self.trader)
                            raw_positions = position_order_data.get("raw_positions", [])
                            raw_orders = position_order_data.get("raw_orders", [])

                            # Calculate total PnL from open positions
                            total_pnl = 0
                            open_positions_for_telegram = []

                            for pos in raw_positions:
                                try:
                                    size = float(pos.get('size', 0))
                                    if size > 0:  # Only count open positions
                                        unrealized_pnl = pos.get('unrealisedPnl', 0)
                                        if unrealized_pnl:
                                            try:
                                                total_pnl += float(unrealized_pnl)
                                            except (ValueError, TypeError):
                                                pass

                                        # Add position data for Telegram
                                        open_positions_for_telegram.append({
                                            'symbol': pos.get('symbol', 'N/A'),
                                            'size': size,  # Add size for proper counting
                                            'pnl_value': float(pos.get('unrealisedPnl', 0)) if pos.get(
                                                'unrealisedPnl') else 0,
                                            'profit_r': 0
                                            # This will be calculated properly in send_position_update
                                        })
                                except (ValueError, TypeError) as e:
                                    logger.info(
                                        f"⚠️ Warning: Could not process position data for {pos.get('symbol', 'Unknown')}: {e}")
                                    continue

                            actual_open_count = len(open_positions_for_telegram)
                            if actual_open_count > 0:
                                logger.info(f"📊 Position monitor: {actual_open_count} open positions found")

                            await telegram_monitor.send_position_update(open_positions_for_telegram, total_pnl, trader=self.trader)
                        except Exception as e:
                            logger.info(f"⚠️ Warning: Could not send Telegram position update: {e}")

                        # Send position update via Telegram
                        try:
                            # Use the utility function to get position and order counts
                            position_order_data = count_open_positions_and_orders(trader=self.trader)
                            raw_positions = position_order_data.get("raw_positions", [])
                            raw_orders = position_order_data.get("raw_orders", [])

                            # Calculate total PnL from open positions
                            total_pnl = 0
                            open_positions_for_telegram = []

                            for pos in raw_positions:
                                try:
                                    size = float(pos.get('size', 0))
                                    if size > 0:  # Only count open positions
                                        unrealized_pnl = pos.get('unrealisedPnl', 0)
                                        if unrealized_pnl:
                                            try:
                                                total_pnl += float(unrealized_pnl)
                                            except (ValueError, TypeError):
                                                pass

                                        # Add position data for Telegram
                                        open_positions_for_telegram.append({
                                            'symbol': pos.get('symbol', 'N/A'),
                                            'size': size,  # Add size for proper counting
                                            'pnl_value': float(pos.get('unrealisedPnl', 0)) if pos.get(
                                                'unrealisedPnl') else 0,
                                            'profit_r': 0
                                            # This will be calculated properly in send_position_update
                                        })
                                except (ValueError, TypeError) as e:
                                    logger.info(
                                        f"⚠️ Warning: Could not process position data for {pos.get('symbol', 'Unknown')}: {e}")
                                    continue

                            actual_open_count = len(open_positions_for_telegram)
                            if actual_open_count > 0:
                                logger.info(f"📊 Position monitor: {actual_open_count} open positions found")

                            await telegram_monitor.send_position_update(open_positions_for_telegram, total_pnl, trader=self.trader)
                        except Exception as e:
                            logger.info(f"⚠️ Warning: Could not send Telegram position update: {e}")

                elif monitor_result.get("status") == "disabled":
                    logger.info(f"⏸️ Position monitor: {monitor_result.get('message')}")

                # Always display the positions table and profit summary if we have position data
                all_positions = monitor_result.get("all_positions", [])
                if all_positions:
                    all_positions.sort(key=lambda x: x["profit_r"], reverse=True)
                    logger.info(f"\n   💰 Current profits: ")
                    profit_strs = [f"{pos['symbol']} {pos['profit_r']:.2f}R" for pos in all_positions]
                    logger.info(", ".join(profit_strs))

                    self.position_analyzer.display_positions_table()

                    # Send position update via Telegram (only when we have positions)
                    try:
                        # Use the utility function to get position and order counts
                        position_order_data = count_open_positions_and_orders(trader=self.trader)
                        raw_positions = position_order_data.get("raw_positions", [])
                        raw_orders = position_order_data.get("raw_orders", [])

                        # Calculate total PnL from open positions
                        total_pnl = 0
                        open_positions_for_telegram = []

                        for pos in raw_positions:
                            try:
                                size = float(pos.get('size', 0))
                                if size > 0:  # Only count open positions
                                    unrealized_pnl = pos.get('unrealisedPnl', 0)
                                    if unrealized_pnl:
                                        try:
                                            total_pnl += float(unrealized_pnl)
                                        except (ValueError, TypeError):
                                            pass

                                    # Add position data for Telegram
                                    open_positions_for_telegram.append({
                                        'symbol': pos.get('symbol', 'N/A'),
                                        'size': size,  # Add size for proper counting
                                        'pnl_value': float(pos.get('unrealisedPnl', 0)) if pos.get(
                                            'unrealisedPnl') else 0,
                                        'profit_r': 0
                                        # This will be calculated properly in send_position_update
                                    })
                            except (ValueError, TypeError) as e:
                                logger.info(
                                    f"⚠️ Warning: Could not process position data for {pos.get('symbol', 'Unknown')}: {e}")
                                continue

                        actual_open_count = len(open_positions_for_telegram)
                        if actual_open_count > 0:
                            logger.info(f"📊 Position monitor: {actual_open_count} open positions found")

                        await telegram_monitor.send_position_update(open_positions_for_telegram, total_pnl, trader=self.trader)
                    except Exception as e:
                        logger.info(f"⚠️ Warning: Could not send Telegram position update: {e}")
                else:
                    logger.info(f"⚠️ Position monitor error: {monitor_result.get('error', 'Unknown error')}")

            except Exception as e:
                logger.info(f"❌ Position monitor exception: {e}")

            await self._sleep_with_shutdown_check(monitor_interval)

    async def get_formatted_positions_status(self) -> str:
        """Generates a formatted string containing position monitor status and open positions table."""
        output_buffer = io.StringIO()
        original_stdout = sys.stdout
        sys.stdout = output_buffer

        try:
            monitor_result = await self.position_monitor.monitor_positions()

            if monitor_result.get("status") == "success":
                positions_checked = monitor_result.get("positions_checked", 0)
                tightenings_applied = monitor_result.get("tightenings_applied", 0)

                logger.info(f"\n📊 Position monitor: Positions checked {positions_checked}")
                if tightenings_applied > 0:
                    logger.info(f"--> {tightenings_applied} TIGHTENINGS APPLIED".upper())
                else:
                    logger.info("--> NO TIGHTENINGS NEEDED".upper())

                all_positions = monitor_result.get("all_positions", [])
                if all_positions:
                    all_positions.sort(key=lambda x: x["profit_r"], reverse=True)
                    profit_strs = [f"{pos['symbol']} {pos['profit_r']:.2f}R" for pos in all_positions]
                    logger.info(f"\n   💰 Current profits: {', '.join(profit_strs)}")
            elif monitor_result.get("status") == "disabled":
                logger.info(f"⏸️ Position monitor: {monitor_result.get('message')}")
            else:
                logger.info(f"⚠️ Position monitor error: {monitor_result.get('error', 'Unknown error')}")

            self.position_analyzer.display_positions_table()

            return output_buffer.getvalue()
        except Exception as e:
            return f"❌ Error generating formatted status: {str(e)}"
        finally:
            sys.stdout = original_stdout

    def _apply_complete_filtering_pipeline(self, timeframe: str, open_positions: Optional[Dict] = None) -> List[Dict]:
        """
        Apply all 5 filters from FilteringPipeline in single pass.
        
        This method replicates the filtering logic from FilteringPipeline but without
        the complex mid-cycle detection, applying all filters in a single pass.
        
        Args:
            timeframe: Target timeframe to filter for
            open_positions: Optional dict of open positions to check conflicts
            
        Returns:
            List of filtered recommendations ready for trading
        """
        try:
            # Get all raw analysis results
            all_analysis = self.data_agent.get_all_latest_analysis()
            if not all_analysis:
                logger.info(f"📊 No analysis data found in database")
                return []
            
            logger.info(f"📊 Raw analysis results: {len(all_analysis)}")
            
            # Filter 1: Current Boundary Filtering - Only timeframe-specific recommendations
            boundary_filtered = [
                rec for rec in all_analysis 
                if rec.get('timeframe') == timeframe
            ]
            boundary_filtered_count = len(all_analysis) - len(boundary_filtered)
            logger.info(f"✅ Current boundary: {len(boundary_filtered)} ({boundary_filtered_count} outside boundary)")
            
            if not boundary_filtered:
                return []
            
            # Filter 2: Timestamp Validation - Using recommender.is_recommendation_valid_timestamp_only()
            timestamp_valid = []
            for rec in boundary_filtered:
                if self.recommender.is_recommendation_valid_timestamp_only(rec):
                    timestamp_valid.append(rec)
            
            timestamp_filtered_count = len(boundary_filtered) - len(timestamp_valid)
            logger.info(f"✅ Timestamp valid: {len(timestamp_valid)} ({timestamp_filtered_count} expired)")
            
            if not timestamp_valid:
                return []
            
            # Filter 3: Recommendation Type Filtering - Filter out HOLD/NEUTRAL
            actionable_recs = []
            for rec in timestamp_valid:
                rec_type = rec.get("recommendation", "").upper()
                if rec_type not in ["HOLD", "NEUTRAL"]:
                    actionable_recs.append(rec)
            
            hold_filtered_count = len(timestamp_valid) - len(actionable_recs)
            logger.info(f"✅ Actionable type: {len(actionable_recs)} ({hold_filtered_count} HOLD filtered)")
            
            if not actionable_recs:
                return []
            
            # Filter 4: Risk Parameter Validation - Using recommender.validate_risk_parameters()
            risk_validated = []
            low_confidence_symbols = []
            low_rr_symbols = []

            for rec in actionable_recs:
                risk_validation = self.recommender.validate_risk_parameters(rec)
                if risk_validation["valid"]:
                    risk_validated.append(rec)
                else:
                    # Track specific risk filtering reasons with symbol details
                    error_msg = risk_validation.get("error", "")
                    symbol = rec.get('symbol', 'Unknown')

                    if "confidence" in error_msg.lower() and "below minimum" in error_msg.lower():
                        # Extract confidence value from recommendation
                        confidence = rec.get('confidence', 0)
                        low_confidence_symbols.append(f"{symbol}: {confidence:.2f}")
                    elif "risk-reward ratio" in error_msg.lower() and "below minimum" in error_msg.lower():
                        # Extract RR value from recommendation or calculate it
                        entry_price = rec.get('entry_price', 0)
                        take_profit = rec.get('take_profit', 0)
                        stop_loss = rec.get('stop_loss', 0)
                        direction = rec.get('direction', 'LONG')

                        if all([entry_price, take_profit, stop_loss]):
                            try:
                                rr_ratio = self.trader.calculate_risk_reward_ratio(
                                    entry_price, take_profit, stop_loss, direction
                                )
                                low_rr_symbols.append(f"{symbol}: {rr_ratio:.2f}")
                            except:
                                low_rr_symbols.append(f"{symbol}: N/A")
                        else:
                            low_rr_symbols.append(f"{symbol}: N/A")

            # Show detailed risk filtering stats in new format
            logger.info(f"✅ Risk validation: {len(risk_validated)}")

            if low_rr_symbols:
                logger.info(f"   Low RR:")
                for symbol_info in low_rr_symbols:
                    logger.info(f"    {symbol_info}")

            if low_confidence_symbols:
                logger.info(f"   Low Confidence:")
                for symbol_info in low_confidence_symbols:
                    logger.info(f"    {symbol_info}")
            
            if not risk_validated:
                return []
            
            # Filter 5: Position Conflict Check - Filter out symbols with existing positions
            final_recommendations = []
            position_filtered_count = 0
            
            for rec in risk_validated:
                symbol = rec.get('symbol')
                if not symbol:
                    continue
                
                # Check if we already have an open position for this symbol
                has_position = False
                if open_positions is not None:
                    # Use batch position data to avoid individual API calls
                    has_position = open_positions.get(symbol, False) or open_positions.get(f"{symbol}.P", False)
                else:
                    # Fallback to individual API call if batch data not provided
                    has_position = self.trader.has_open_position(symbol)
                
                if not has_position:
                    final_recommendations.append(rec)
                else:
                    position_filtered_count += 1
            
            logger.info(f"✅ No position conflicts: {len(final_recommendations)} ({position_filtered_count} existing positions)")
            logger.info(f"\n🎯 Final result: {len(final_recommendations)} recommendations ready for trading")
            
            return final_recommendations
            
        except Exception as e:
            logger.info(f"❌ Error in complete filtering pipeline: {e}")
            return []

    def _initialize_intelligent_systems(self):
        """Initialize intelligent order replacement system."""
        intelligent_replacement = None
        if hasattr(self, 'trader') and hasattr(self, 'position_manager'):
            try:
                intelligent_replacement = IntelligentOrderReplacementSystem(
                    trader=self.trader,
                    position_manager=self.position_manager,
                    config=self.config,
                    logger=None,
                    data_agent=getattr(self.position_manager, 'data_agent', None)
                )
                logger.info("✅ Intelligent order replacement system initialized")
            except Exception as e:
                logger.info(f"⚠️ Warning: Could not initialize intelligent order replacement: {e}")
        return intelligent_replacement

    async def _perform_cycle_setup(self, cycle_count: int, timeframe: str, dry_run: bool):
        """Handle cycle initialization and setup tasks efficiently."""
        current_time = datetime.now(timezone.utc)

        logger.info(f"\n{'*' * 60}")
        logger.info(f"🔄 Cycle #{cycle_count} - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"{'*' * 60}")

        # Only reload config if needed (not every cycle)
        if cycle_count == 1 or cycle_count % 10 == 0:  # Every 10 cycles or first cycle
            self.reload_config()

        # Lightweight sync check
        await self._sync_with_exchange()

        # Create workflow state and initialize Telegram monitor
        workflow_state = create_workflow_state(timeframe, current_time)
        telegram_monitor = TelegramMonitor()

        # Perform maintenance tasks (optimized frequency)
        await self._perform_maintenance_tasks(telegram_monitor, cycle_count, timeframe, current_time)

        return current_time, workflow_state, telegram_monitor

    async def _execute_trading_workflow(self, timeframe: str, workflow_state: dict, validator, current_time: datetime, intelligent_replacement) -> tuple:
        """Execute the complete trading workflow with unified error handling."""
        trade_signals = []
        final_signals = []
        position_sizes = []

        # Helper function to ensure valid workflow state
        def ensure_valid_workflow_state(state, step_name):
            if state is None or state.get("status") == "error":
                logger.info(f"🔄 Creating new workflow state for {step_name}")
                return update_workflow_state(create_workflow_state(timeframe, current_time), step_name)
            return state

        try:
            # STEP 1: Fresh Data Check
            workflow_state = await self._handle_fresh_data_check(timeframe, workflow_state)
            workflow_state = ensure_valid_workflow_state(workflow_state, "fresh_data_check")

            # Check if we should wait for next boundary (mid-cycle with no fresh data)
            if workflow_state.get("should_wait_for_boundary"):
                logger.info("⏸️ Workflow paused - waiting for next cycle boundary")
                next_boundary = workflow_state.get("next_boundary")
                if next_boundary:
                    await self._sleep_until_next_cycle(current_time, timeframe, validator)
                return workflow_state, [], []

            if shutdown_requested:
                return workflow_state, final_signals, position_sizes

            # STEP 2: Apply Complete Filtering Pipeline
            trade_signals, workflow_state = await self._handle_filtering_pipeline(
                timeframe, workflow_state, validator, current_time
            )
            trade_signals = trade_signals or []
            workflow_state = ensure_valid_workflow_state(workflow_state, "filtering")

            if shutdown_requested:
                return workflow_state, final_signals, position_sizes

            # STEP 3: Global Slot Optimization
            final_signals, workflow_state = await self._handle_slot_optimization(
                trade_signals, timeframe, False, workflow_state, intelligent_replacement
            )
            final_signals = final_signals or []
            workflow_state = ensure_valid_workflow_state(workflow_state, "intelligent_replacement")

            if shutdown_requested:
                return workflow_state, final_signals, position_sizes

            # STEP 4: Position Sizing
            position_sizes, workflow_state = await self._handle_position_sizing(final_signals, workflow_state)
            position_sizes = position_sizes or []
            workflow_state = ensure_valid_workflow_state(workflow_state, "position_sizing")

        except Exception as e:
            logger.error(f"❌ Trading workflow failed: {e}")
            # Return safe defaults
            workflow_state = ensure_valid_workflow_state(None, "error_recovery")

        return workflow_state, final_signals, position_sizes

    async def _sync_with_exchange(self):
        """Lightweight sync with exchange - optimized for performance."""
        try:
            # Only do full sync periodically, otherwise just check timestamp sync
            current_time = time.time()

            # Check if we need timestamp sync (every 2 minutes max)
            if not hasattr(self, '_last_timestamp_sync') or (current_time - self._last_timestamp_sync) > 120:
                sync_status = self.trader.api_manager.get_timestamp_sync_status()
                if sync_status.get("status") == "success":
                    sync_data = sync_status.get("sync_data", {})
                    last_sync_ago = sync_data.get("last_sync_ago_seconds", 0)

                    # Only force sync if really needed (more than 60 seconds)
                    if last_sync_ago > 60:
                        self.trader.api_manager.force_timestamp_sync()

                self._last_timestamp_sync = current_time

            # Do incremental sync more frequently (every 2 minutes) - much more efficient
            if not hasattr(self, '_last_incremental_sync') or (current_time - self._last_incremental_sync) > 120:
                sync_result = self.perform_incremental_sync()
                if sync_result.get('status') == 'success':
                    logger.info(f"✅ Incremental sync completed - {sync_result.get('trades_updated', 0)} updated, {sync_result.get('trades_created', 0)} created")
                self._last_incremental_sync = current_time

        except Exception as e:
            logger.warning(f"⚠️ Sync failed: {e}")

    async def _perform_maintenance_tasks(self, telegram_monitor, cycle_count, timeframe, current_time):
        """Perform maintenance tasks with optimized frequency."""
        # Clean outdated chart files (every 5 cycles to reduce overhead)
        if cycle_count % 5 == 1:
            logger.info("🧹 Cleaning outdated chart files...")
            try:
                cleaning_summary = self.cleaner.get_cleaning_summary(self.config.paths.charts)
                if cleaning_summary['outdated_files'] > 0:
                    deleted_files = self.cleaner.clean_outdated_files(self.config.paths.charts, dry_run=False)
                    logger.info(f"✅ Cleaned {len(deleted_files)} outdated chart files")
            except Exception as e:
                logger.info(f"⚠️ Warning: Chart cleaning failed: {str(e)}")

        # Send notifications less frequently (every 10 cycles)
        if cycle_count % 10 == 1:
            await telegram_monitor.send_cycle_summary({
                "cycle_count": cycle_count,
                "timeframe": timeframe,
                "start_time": current_time.isoformat(),
                "message": f"Autotrader health check - Cycle #{cycle_count}"
            })

            # Send system health update
            await self._send_system_health_update(telegram_monitor)

    async def _send_system_health_update(self, telegram_monitor):
        """Send system health update via Telegram."""
        try:
            if psutil is None:
                return
            health_data = {
                "status": "healthy",
                "services": {
                    "autotrader": "running",
                    "telegram": "connected",
                    "tradingview": "ready" if self.sourcer and self.sourcer.tradingview_enabled else "disabled"
                },
                "metrics": {
                    "cpu_percent": psutil.cpu_percent(),
                    "memory_percent": psutil.virtual_memory().percent,
                    "disk_percent": psutil.disk_usage('/').percent
                }
            }
            await telegram_monitor.send_system_health(health_data)
        except Exception as e:
            logger.info(f"⚠️ Warning: Could not send system health update: {e}")

    async def _handle_fresh_data_check(self, timeframe: str, workflow_state):
        """Handle fresh data check and chart capture/analysis if needed."""
        log_step_header("Fresh data check", 1)
        workflow_state = update_workflow_state(workflow_state, "fresh_data_check")

        has_fresh_data = self.recommendation_service.is_fresh_data_available(timeframe)

        if has_fresh_data:
            logger.info("✅ Fresh recommendations found - skipping chart capture")
            return workflow_state

        # Check if we're mid-cycle - if so, wait for next boundary instead of capturing charts
        from datetime import datetime, timezone
        from trading_bot.core.timestamp_validator import TimestampValidator

        current_time = datetime.now(timezone.utc)
        validator = TimestampValidator()
        timeframe_info = validator.normalize_timeframe(timeframe)
        next_boundary = validator.calculate_next_boundary(current_time, timeframe)
        current_boundary = next_boundary - timeframe_info.timedelta

        # Check if we're more than 1% through the cycle (mid-cycle)
        total_seconds = timeframe_info.timedelta.total_seconds()
        elapsed_seconds = (current_time - current_boundary).total_seconds()
        cycle_progress = elapsed_seconds / total_seconds

        if cycle_progress > 0.01:  # More than 1% through cycle = mid-cycle
            logger.info(f"⏸️ Mid-cycle detected ({cycle_progress:.1%} through {timeframe} cycle)")
            logger.info(f"⏸️ No fresh recommendations for current boundary - waiting for next cycle")
            logger.info(f"⏸️ Next boundary: {next_boundary.strftime('%H:%M:%S')} UTC")

            # Return a special status to indicate we should wait
            workflow_state["should_wait_for_boundary"] = True
            workflow_state["next_boundary"] = next_boundary
            return workflow_state

        logger.info("🔄 No fresh recommendations - capturing charts")
        chart_result = await self.get_charts(timeframe_check=timeframe, force_capture=True)
        if chart_result.get("status") != "success":
            raise Exception(f"Chart capture failed: {chart_result.get('error')}")

        logger.info("🔄 Analyzing captured charts...")
        analysis_results = self.analyze_all_images(self.config.paths.charts, target_timeframe=timeframe)

        # Log analysis summary
        if analysis_results:
            successful_analyses = len([r for r in analysis_results if not r.get("error", False)])
            total_analyses = len(analysis_results)
            logger.info(f"✅ Chart analysis completed: {successful_analyses}/{total_analyses} successful analyses")
        else:
            logger.warning("⚠️ Chart analysis returned no results")
            logger.info("🔄 Continuing with workflow despite empty analysis results")

        return workflow_state

    async def _handle_filtering_pipeline(self, timeframe: str, workflow_state, validator, current_time):
        """Apply complete filtering pipeline and handle empty results."""
        log_step_header("Applying complete filtering pipeline", 2)
        workflow_state = update_workflow_state(workflow_state, "filtering")

        # Get cached positions for filtering
        actual_positions, open_positions = self.get_cached_positions()

        # Apply all 5 filters from FilteringPipeline in single pass
        final_recommendations = self._apply_complete_filtering_pipeline(timeframe, open_positions)

        if not final_recommendations:
            logger.info("📈 No valid recommendations after filtering - completing cycle and waiting for next boundary")
            return [], workflow_state

        # Convert to signal format for next steps
        trade_signals = []
        for rec in final_recommendations:
            signal_data = create_signal_data(rec, timeframe)
            trade_signals.append(signal_data)

        workflow_state = update_workflow_state(
            workflow_state,
            "filtering",
            data_updates={"trade_signals": trade_signals}
        )

        return trade_signals, workflow_state

    async def _handle_slot_optimization(self, trade_signals, timeframe, dry_run, workflow_state, intelligent_replacement):
        """Handle global slot optimization with intelligent replacement using unified SlotManager."""
        log_step_header("Global slot optimization", 3)

        # Use unified SlotManager for consistent slot counting
        available_order_slots, slot_details = self.slot_manager.get_available_order_slots()

        logger.info(f"📊 Slot optimization: {available_order_slots} available order slots")
        logger.info(f"   Current positions: {slot_details.get('open_positions', 0)}")
        logger.info(f"   Current entry orders: {slot_details.get('current_entry_orders', 0)}")
        logger.info(f"   Max concurrent trades: {slot_details.get('max_concurrent_trades', 5)}")

        # Handle slot exhaustion
        if available_order_slots <= 0:
            logger.info("🚨 NO ORDER SLOTS AVAILABLE - attempting automatic cancellation")
            available_order_slots = await self._handle_slot_exhaustion(slot_details)
            if available_order_slots <= 0:
                logger.info("❌ Unable to free up slots - skipping cycle")
                return [], workflow_state

        workflow_state = update_workflow_state(workflow_state, "intelligent_replacement")

        # Run intelligent replacement with corrected slot count
        final_signals = await self._run_intelligent_replacement(
            trade_signals, timeframe, dry_run, available_order_slots, intelligent_replacement
        )

        if not final_signals:
            logger.info("📈 No final signals after intelligent replacement - completing cycle and waiting for next boundary")
            return [], workflow_state

        workflow_state = update_workflow_state(
            workflow_state,
            "intelligent_replacement",
            data_updates={"final_signals": final_signals}
        )

        return final_signals, workflow_state

    async def _handle_slot_exhaustion(self, slot_validation):
        """Handle slot exhaustion by cancelling excess orders."""
        excess_count = abs(slot_validation.get('available_slots', 0))
        logger.info(f"🔄 Automatically cancelling {excess_count} excess entry order(s)...")

        try:
            cancel_results = await self.position_monitor._cancel_excess_entry_orders(excess_count)
            successful_cancels = len([r for r in cancel_results if r.get('status') == 'cancelled'])

            logger.info(f"✅ Successfully cancelled {successful_cancels} excess entry order(s)")

            # Re-validate slots
            updated_slot_validation = await self.risk_manager.get_available_slots_with_retry()
            updated_available_slots = updated_slot_validation.get('available_slots', 0)
            logger.info(f"✅ Updated slot status: {updated_available_slots} available")

            return updated_available_slots if updated_available_slots > 0 else 0
        except Exception as e:
            logger.info(f"❌ Error during automatic cancellation: {e}")
            return 0

    async def _run_intelligent_replacement(self, trade_signals, timeframe, dry_run, available_slots, intelligent_replacement):
        """Run intelligent order replacement system."""
        if not trade_signals:
            return []

        logger.info(f"🧠 {available_slots} slots available - running intelligent replacement...")

        if intelligent_replacement:
            optimization_result = intelligent_replacement.execute_global_slot_optimization(
                trade_signals, timeframe, dry_run=dry_run
            )

            if optimization_result['success']:
                final_signals = optimization_result.get('final_signals', trade_signals)
                cancelled_orders = optimization_result.get('cancelled_orders', [])

                logger.info("✅ Intelligent replacement complete:")
                logger.info(f"   📊 Final signals: {len(final_signals)}")
                logger.info(f"   ❌ Cancelled orders: {len(cancelled_orders)}")

                return final_signals
            else:
                logger.info(f"⚠️ Intelligent replacement failed: {optimization_result.get('error', 'Unknown error')}")
                return trade_signals
        else:
            logger.info("⚠️ Intelligent replacement system not available")
            return trade_signals

    async def _handle_position_sizing(self, final_signals, workflow_state):
        """Handle position sizing for confirmed trades."""
        log_step_header("Position sizing", 4)
        workflow_state = update_workflow_state(workflow_state, "position_sizing")

        logger.info("🔍 Fetching batch order data for position sizing...")
        orders_data = self.trader.get_open_orders()

        position_sizing_result = calculate_position_sizes_for_batch(
            self.risk_manager, final_signals, None, orders_data
        )

        # Show position sizing results
        if position_sizing_result.get('position_sizes'):
            for i, result in enumerate(position_sizing_result['position_sizes']):
                success = result.get('success', False)
                symbol = result.get('symbol', 'Unknown')
                error = result.get('error', 'No error')
                logger.info(f"   {i + 1}. {symbol}: {'✅ Success' if success else f'❌ Failed - {error}'}")

        if position_sizing_result.get("status") != "success":
            raise Exception(f"Position sizing failed: {position_sizing_result.get('message', 'Unknown error')}")

        position_sizes = position_sizing_result.get("position_sizes", [])
        total_risk = position_sizing_result.get("total_risk_allocated", 0)

        num_positions = len(position_sizes)
        per_trade_risk_pct = total_risk / num_positions if num_positions > 0 else 0

        logger.info(f"✅ Position sizing: {num_positions} positions, {total_risk:.3f}% total risk ({per_trade_risk_pct:.3f}% per trade)")

        workflow_state = update_workflow_state(
            workflow_state,
            "position_sizing",
            data_updates={"position_sizes": position_sizes},
            metrics_updates={"total_risk_allocated": total_risk}
        )

        return position_sizes, workflow_state

    async def _handle_telegram_confirmation(self, final_signals, position_sizes, timeframe, dry_run, workflow_state):
        """Handle Telegram confirmation for trades."""
        log_step_header("Telegram confirmation", 5)
        workflow_state = update_workflow_state(workflow_state, "telegram_confirmation")

        confirmed_trades = []

        for i, (signal, position_result) in enumerate(zip(final_signals, position_sizes), 1):
            if shutdown_requested:
                break

            if not position_result.get("success"):
                symbol = signal.get("symbol", "Unknown")
                logger.info(f"⏭️ Skipping trade {i} due to position sizing failure for {symbol}")
                continue

            symbol = signal.get("symbol", "Unknown")
            direction = signal.get("direction", "UNKNOWN").upper()

            logger.info(f"📱 Processing trade {i}/{len(final_signals)}: {direction} {symbol}")

            # Create trade data for Telegram
            trade_data = create_trade_data(signal, timeframe)
            position_size = position_result.get('position_size', 0)

            if dry_run:
                logger.info(f"🔄 DRY RUN: Would send Telegram confirmation for {direction} {symbol}")
                confirmed_trades.append((signal, position_result, True, None))
            elif self.config.trading.auto_approve_trades:
                logger.info(f"🤖 AUTO-APPROVE MODE: Automatically approving trade for {direction} {symbol}")
                confirmed_trades.append((signal, position_result, True, None))
            else:
                # Send Telegram confirmation
                logger.info(f"📱 Sending Telegram confirmation for {direction} {symbol}...")

                try:
                    telegram_bot = TelegramBot()
                    confirmation = await telegram_bot.send_trade_with_confirmation(trade_data, timeout=60)

                    if confirmation is True:
                        logger.info(f"✅ Trade confirmed by user for {symbol}")
                        confirmed_trades.append((signal, position_result, True, None))
                    elif confirmation is False:
                        logger.info(f"❌ Trade rejected by user for {symbol}")
                        confirmed_trades.append((signal, position_result, False, None))
                    else:
                        logger.info(f"⏰ Telegram confirmation timeout for {symbol}")
                        confirmed_trades.append((signal, position_result, False, None))

                except Exception as e:
                    logger.info(f"❌ Telegram confirmation error for {symbol}: {str(e)}")
                    confirmed_trades.append((signal, position_result, False, None))

        workflow_state = update_workflow_state(
            workflow_state,
            "telegram_confirmation",
            data_updates={"confirmed_trades": confirmed_trades}
        )

        return confirmed_trades, workflow_state

    async def _handle_trade_placement(self, confirmed_trades, dry_run, workflow_state):
        """Handle final trade placement."""
        log_step_header("Trade placement", 6)
        workflow_state = update_workflow_state(workflow_state, "trade_placement")

        executed_count = 0

        for signal, position_result, confirmed, alteration_details in confirmed_trades:
            if shutdown_requested or not confirmed:
                continue

            symbol = signal.get("symbol", "Unknown")
            direction = signal.get("direction", "UNKNOWN").upper()

            if dry_run:
                logger.info(f"🔄 DRY RUN: Would execute trade for {direction} {symbol}")
                executed_count += 1
            else:
                logger.info(f"🚀 Executing live trade for {direction} {symbol}...")

                live_result = self.trader.execute_trade(
                    signal=signal,
                    symbol=symbol,
                    order_type="Limit",
                    dry_run=False,
                    alteration_details=alteration_details
                )

                if live_result.get("status") == "executed_live":
                    logger.info(f"🎉 Trade executed successfully for {direction} {symbol}")
                    # Invalidate position cache to ensure fresh data for subsequent checks
                    self.invalidate_position_cache()
                    executed_count += 1
                else:
                    logger.info(f"❌ Trade execution failed for {direction} {symbol}: {live_result.get('error', 'Unknown error')}")

        workflow_state = update_workflow_state(
            workflow_state,
            "trade_placement",
            metrics_updates={"executed_trades": executed_count},
            status="completed"
        )

        return executed_count, workflow_state

    async def _complete_cycle(self, final_signals, confirmed_trades, executed_count, dry_run, current_time, timeframe, validator):
        """Complete cycle with summary and sleep until next boundary."""
        logger.info(create_cycle_summary(len(final_signals), len([t for t in confirmed_trades if t[2]]), executed_count, dry_run))

        # Sleep until next boundary (exact timing for cycle synchronization)
        next_boundary = validator.calculate_next_boundary(current_time, timeframe, add_random_delay=False)
        sleep_duration = calculate_sleep_until_boundary(current_time, next_boundary)
        if sleep_duration > 0:
            boundary_display = format_utc_time_for_display(next_boundary, "%H:%M:%S")
            logger.info(f"\n⏰ Cycle complete. Sleeping until next {timeframe} boundary: {boundary_display}")
            logger.info(f"💤 Sleep duration: {sleep_duration / 60:.1f} minutes")
            await self._sleep_with_shutdown_check(sleep_duration)
        else:
            logger.info(f"\n⏰ Already at {timeframe} boundary - starting next cycle immediately")

    async def _sleep_until_next_cycle(self, current_time, timeframe, validator):
        """Sleep until next cycle boundary."""
        next_boundary = validator.calculate_next_boundary(current_time, timeframe, add_random_delay=False)
        sleep_duration = calculate_sleep_until_boundary(current_time, next_boundary)
        if sleep_duration > 0:
            boundary_display = format_utc_time_for_display(next_boundary, "%H:%M:%S")
            logger.info(f"\n⏰ Sleeping until next {timeframe} boundary: {boundary_display}")
            logger.info(f"💤 Sleep duration: {sleep_duration / 60:.1f} minutes")
            await self._sleep_with_shutdown_check(sleep_duration)

    async def run_autotrader(self, timeframe: str, dry_run: bool = False) -> None:
        """
        Run autotrader with simplified 6-step workflow for specified timeframe.
        """
        global shutdown_requested

        validator = TimestampValidator()
        intelligent_replacement = self._initialize_intelligent_systems()

        self._print_workflow_header("SIMPLIFIED AUTOTRADER STARTED", dry_run)
        logger.info(f"📊 Timeframe: {timeframe}")
        logger.info(f"⏰ Started at: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
        logger.info(f"🔧 Workflow: Simplified 6-step process")
        logger.info(f"Press Ctrl+C to stop gracefully...")

        cycle_count = 0
        position_monitor_task = None

        if hasattr(self, 'position_monitor'):
            position_monitor_task = asyncio.create_task(self._run_position_monitor_background())

        try:
            while not shutdown_requested:
                try:
                    cycle_count += 1
                    current_time, workflow_state, telegram_monitor = await self._perform_cycle_setup(
                        cycle_count, timeframe, dry_run
                    )

                    if shutdown_requested:
                        break

                    # STEP 0: Cycle-Level Slot Check (NEW - implements your requested logic)
                    log_step_header("Cycle-level slot check", 0)
                    should_skip, skip_reason = self.slot_manager.should_skip_cycle_due_to_slots(timeframe, current_time)

                    if should_skip:
                        logger.info(f"🚫 CYCLE SKIPPED: {skip_reason}")
                        await self._sleep_until_next_cycle(current_time, timeframe, validator)
                        continue

                    # Log current slot status for transparency
                    slot_status = self.slot_manager.get_current_slot_status()
                    logger.info(f"   📊 Current status: {slot_status.get('open_positions')}/{slot_status.get('max_slots')} positions open")
                    logger.info(f"   📊 Slot Breakdown: {slot_status.get('slot_breakdown', {})}")

                    if shutdown_requested:
                        break

                    # Execute workflow steps with unified error handling
                    workflow_state, final_signals, position_sizes = await self._execute_trading_workflow(
                        timeframe, workflow_state, validator, current_time, intelligent_replacement
                    )

                    if shutdown_requested:
                        break

                    # STEP 5: Telegram Confirmation
                    confirmed_trades, workflow_state = await self._handle_telegram_confirmation(
                        final_signals, position_sizes, timeframe, dry_run, workflow_state
                    )

                    if shutdown_requested:
                        break

                    # STEP 6: Trade Placement
                    executed_count, workflow_state = await self._handle_trade_placement(
                        confirmed_trades, dry_run, workflow_state
                    )

                    # STEP 7: Duplicate Cleanup (SINGLE SOURCE OF TRUTH)
                    if not dry_run:
                        log_step_header("Exchange duplicate cleanup", 7)
                        cleanup_result = self.trader.cleanup_exchange_duplicates()

                        if cleanup_result.get("success"):
                            cancelled_count = cleanup_result.get("summary", {}).get("total_cancelled", 0)
                            error_count = cleanup_result.get("summary", {}).get("total_errors", 0)
                            logger.info(f"🧹 Duplicate cleanup: {cancelled_count} orders cancelled, {error_count} errors")
                        else:
                            logger.error(f"❌ Duplicate cleanup failed: {cleanup_result.get('error', 'Unknown error')}")

                    # Complete cycle
                    await self._complete_cycle(
                        final_signals, confirmed_trades, executed_count, dry_run,
                        current_time, timeframe, validator
                    )

                    # Reset error count on successful cycle completion
                    if hasattr(self, '_consecutive_errors'):
                        self._consecutive_errors = 0

                    # Continue to next cycle (the _complete_cycle method handles sleeping)
                    continue

                except Exception as e:
                    logger.error(f"❌ Unexpected error in cycle {cycle_count}: {e}")

                    # Enhanced error recovery with exponential backoff
                    error_count = getattr(self, '_consecutive_errors', 0) + 1
                    self._consecutive_errors = error_count

                    if error_count <= 3:
                        # Short delay for first few errors
                        sleep_time = min(30 * error_count, 120)  # Max 2 minutes
                        logger.info(f"🔄 Error #{error_count} - sleeping {sleep_time}s before retry...")
                        await self._sleep_with_shutdown_check(sleep_time)
                    else:
                        # Longer delay for persistent errors
                        logger.error(f"❌ {error_count} consecutive errors - extended recovery mode")
                        await self._sleep_with_shutdown_check(300)  # 5 minutes

                        # Reset error count after extended delay
                        if error_count >= 5:
                            self._consecutive_errors = 0
                            logger.info("🔄 Error count reset after extended recovery")

                    continue

        except KeyboardInterrupt:
            logger.info("\n🛑 Keyboard interrupt received")
        finally:
            if position_monitor_task and not position_monitor_task.done():
                position_monitor_task.cancel()
                try:
                    await position_monitor_task
                except asyncio.CancelledError:
                    pass

            logger.info(f"\n{'=' * 60}")
            logger.info("🛑 SIMPLIFIED AUTOTRADER STOPPED")
            logger.info(f"📊 Total cycles completed: {cycle_count}")
            logger.info(f"⏰ Stopped at: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
            logger.info(f"{'=' * 60}")

    async def start_realtime_monitoring(self):
        """Start real-time trade monitoring system."""
        try:
            if hasattr(self.position_manager, 'trade_monitor') and self.position_manager.trade_monitor:
                await self.position_manager.trade_monitor.start_monitoring()
                logger.info("✅ Real-time trade monitoring started")
            else:
                logger.warning("⚠️ Real-time trade monitor not available")
        except Exception as e:
            logger.error(f"❌ Failed to start real-time monitoring: {e}")

    async def stop_realtime_monitoring(self):
        """Stop real-time trade monitoring system."""
        try:
            if hasattr(self.position_manager, 'trade_monitor') and self.position_manager.trade_monitor:
                await self.position_manager.trade_monitor.stop_monitoring()
                logger.info("✅ Real-time trade monitoring stopped")
        except Exception as e:
            logger.error(f"❌ Failed to stop real-time monitoring: {e}")

    def perform_incremental_sync(self) -> dict:
        """Perform efficient incremental sync instead of expensive rebuild."""
        try:
            if self.incremental_sync_manager:
                # Use incremental sync for efficiency
                stats = self.incremental_sync_manager.sync_new_and_updated_trades(lookback_hours=2)
                logger.info(f"✅ Incremental sync completed: {stats.trades_updated} updated, {stats.trades_created} created")
                return {
                    "status": "success",
                    "sync_type": "incremental",
                    "trades_updated": stats.trades_updated,
                    "trades_created": stats.trades_created,
                    "duration": stats.sync_duration
                }
            else:
                # Fallback to rebuild sync if incremental not available
                logger.warning("⚠️ Incremental sync not available, using rebuild sync")
                result = asyncio.run(self.rebuild_and_sync_manager.sync_with_exchange(rebuild=False))
                return result
        except Exception as e:
            logger.error(f"❌ Sync failed: {e}")
            return {"status": "error", "error": str(e)}


def autotrader(timeframe: str, dry_run=False, clear_logs=False):
    """Run the autotrader for specified timeframe."""
    try:
        # Clear logs folder if requested
        if clear_logs:
            import shutil
            # Get logs path from config
            config = Config.from_yaml()
            logs_dir = config.paths.logs
            if os.path.exists(logs_dir):
                # Use a basic logger for the clearing process since we might be reconfiguring the main logger
                temp_logger = logging.getLogger('temp_logger')
                temp_logger.setLevel(logging.INFO)
                if not temp_logger.handlers:
                    handler = logging.StreamHandler()
                    handler.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
                    temp_logger.addHandler(handler)

                temp_logger.info("🧹 Clearing logs folder...")
                try:
                    shutil.rmtree(logs_dir)
                    os.makedirs(logs_dir)
                    temp_logger.info("✅ Logs folder cleared successfully")

                    # Reconfigure logging after clearing logs directory
                    temp_logger.info("🔄 Reconfiguring logging after logs cleanup...")
                    from trading_bot.core.logging_config import configure_run_autotrader_logging
                    # Reassign the global logger variable (it will create its own file handler)
                    global logger
                    logger = configure_run_autotrader_logging()
                    logger.info("✅ Logging reconfigured successfully")
                except Exception as e:
                    temp_logger.warning(f"⚠️ Warning: Failed to clear logs folder: {e}")
            else:
                # Use basic logging for this message since we haven't reconfigured yet
                basic_logger = logging.getLogger('basic_logger')
                basic_logger.setLevel(logging.INFO)
                if not basic_logger.handlers:
                    handler = logging.StreamHandler()
                    handler.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
                    basic_logger.addHandler(handler)
                basic_logger.info("📁 Logs folder doesn't exist, no clearing needed")

        # Initialize bot
        bot = SimpleTradingBot(use_testnet=False)
        bot.db_queue.start()

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Check configuration
        if bot.config.trading.auto_approve_trades:
            logger.info("🤖 AUTO-APPROVE MODE ENABLED: Trades will be executed automatically without Telegram confirmation")
        else:
            logger.info("📱 TELEGRAM CONFIRMATION MODE: Trades will require user confirmation via Telegram")

        logger.info(f"🚀 Starting {timeframe} autotrader...")
        logger.info("Press Ctrl+C to stop gracefully...")

        try:
            asyncio.run(bot.run_autotrader(timeframe, dry_run=dry_run))
        except KeyboardInterrupt:
            logger.info("\n🛑 Autotrader stopped by user")
        except Exception as e:
            logger.error(f"❌ Autotrader error: {str(e)}")
            return 1

    except Exception as e:
        logger.error(f"❌ Initialization error: {str(e)}")
        return 1
    finally:
        if 'bot' in locals():
            bot.db_queue.stop()
            bot.data_agent.close_connection()

    return 0


if __name__ == "__main__":
    # Run 1h autotrader by default
    exit_code = autotrader("1h", dry_run=False, clear_logs=True)
    sys.exit(exit_code)
